# 导入必要的库
import ee
import geemap

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")

# 创建交互式地图
Map = geemap.Map()

print("地图已创建")

# 选择区域
path = r'G:\Heritage\Changcheng\陕西神木_1km_part8.shp'
roi = geemap.shp_to_ee(path)

geometry = roi.geometry().bounds()

# 将地图中心设置到选定区域
Map.centerObject(geometry, 12)
Map.addLayer(geometry, {'color': 'red'}, 'Selected Region', False)

print("已选择陕西神木作为研究区域")

Map

# 加载卫星嵌入数据集
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

# 设置年份和日期范围
year = 2022
start_date = ee.Date.fromYMD(year, 1, 1)
end_date = start_date.advance(1, 'year')

print(f"数据过滤设置：")
print(f"- 目标年份：{year}")
print(f"- 开始日期：{year}-01-01")
print(f"- 结束日期：{year+1}-01-01")

# 应用时间和空间过滤
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

# 创建镶嵌图
embeddings_image = filtered_embeddings.mosaic()

print("- 已应用时间和空间过滤器")
print("- 已创建嵌入数据镶嵌图")

# 使用Cropland Data Layers (CDL)获取耕地掩码
cdl = ee.ImageCollection('USDA/NASS/CDL') \
    .filter(ee.Filter.date(start_date, end_date)) \
    .first()

crop_landcover = cdl.select('cropland')
cropland_mask = cdl.select('cultivated').eq(2).rename('cropmask')

# 可视化作物掩码
cropland_mask_vis = {'min': 0, 'max': 1, 'palette': ['white', 'green']}
Map.addLayer(cropland_mask.clip(geometry), cropland_mask_vis, 'Crop Mask')

print("已创建农作物地块掩码")
print("- 绿色区域表示耕地")
print("- 白色区域表示非耕地")

Map

# 将掩码应用于嵌入图像，只保留耕地像素
cluster_image = embeddings_image.updateMask(cropland_mask)

print("已将农作物掩码应用于嵌入图像")

# 分层随机采样
# 为确保提取所需数量的非null样本，使用分层抽样在未遮盖的区域中获取样本
training = cluster_image.addBands(cropland_mask).stratifiedSample(**{
    'numPoints': 1000,
    'classBand': 'cropmask',
    'region': geometry,
    'scale': 10,
    'tileScale': 16,
    'seed': 100,
    'dropNulls': True,
    'geometries': True
})

print("已完成分层随机采样")
print("- 采样点数：1000")
print("- 比例尺：10米")
print("- 已排除null值")

training

# 可视化提取的样本
print("提取的第一个样本:")
# 注意：在Python中，我们可以通过getInfo()来获取样本信息
# print(training.first().getInfo())  # 取消注释以查看样本详情

# 将训练点添加到地图
Map.addLayer(training, {'color': 'blue'}, 'Extracted Samples', False)

print("已将训练样本添加到地图（蓝色点）")

Map

# 对卫星嵌入图像进行聚类
# 根据当地知识设置聚类数量范围
min_clusters = 4
max_clusters = 5

print(f"聚类参数设置:")
print(f"- 最小聚类数：{min_clusters}")
print(f"- 最大聚类数：{max_clusters}")

# 创建并训练CascadeKMeans聚类器
clusterer = ee.Clusterer.wekaCascadeKMeans(**{
    'minClusters': min_clusters, 
    'maxClusters': max_clusters
}).train(**{
    'features': training,
    'inputProperties': cluster_image.bandNames()
})

# 对图像进行聚类
clustered = cluster_image.cluster(clusterer)

# 添加聚类结果到地图
Map.addLayer(clustered.randomVisualizer().clip(geometry), {}, 'Clusters')

print("已完成无监督聚类分析")
print("- 聚类结果已添加到地图")

Map

# 计算聚类面积
# 1英亩 = 4046.86平方米
area_image = ee.Image.pixelArea().divide(4046.86).addBands(clustered)

areas = area_image.reduceRegion(**{
    'reducer': ee.Reducer.sum().group(**{
        'groupField': 1,
        'groupName': 'cluster'
    }),
    'geometry': geometry,
    'scale': 10,
    'maxPixels': 1e10
})

cluster_areas = ee.List(areas.get('groups'))

print("已计算各聚类面积")

# 处理结果以提取面积并创建FeatureCollection
def process_cluster_area(item):
    area_dict = ee.Dictionary(item)
    cluster_number = area_dict.getNumber('cluster').format()
    area = area_dict.getNumber('sum')
    return ee.Feature(None, {'cluster': cluster_number, 'area': area})

cluster_areas_processed = cluster_areas.map(process_cluster_area)
cluster_area_fc = ee.FeatureCollection(cluster_areas_processed)

print("聚类面积统计:")
# print(cluster_area_fc.getInfo())  # 取消注释以查看详细面积信息

# 选择面积最大的2个聚类
selected_fc = cluster_area_fc.sort('area', False).limit(2)
print("已选择面积最大的2个聚类作为主要作物类型")

# 分配作物标签
# 根据2022年爱荷华州Cerro Gordo County的作物统计：
# - 玉米种植面积：161,500英亩
# - 大豆种植面积：110,500英亩
# 因此面积最大的聚类应该是玉米，第二大的是大豆

corn_feature = selected_fc.sort('area', False).first()
soybean_feature = selected_fc.sort('area').first()

corn_cluster = corn_feature.get('cluster')
soybean_cluster = soybean_feature.get('cluster')

print("作物标签分配:")
print(f"- 玉米聚类ID: {corn_cluster.getInfo()}")
print(f"- 大豆聚类ID: {soybean_cluster.getInfo()}")

# 显示检测到的面积与官方统计的比较
corn_area_detected = corn_feature.getNumber('area').round()
soybean_area_detected = soybean_feature.getNumber('area').round()

print(f"\n面积比较（英亩）:")
print(f"玉米 - 检测到: {corn_area_detected.getInfo()}, 官方统计: 161,500")
print(f"大豆 - 检测到: {soybean_area_detected.getInfo()}, 官方统计: 110,500")

# 选择聚类以创建作物地图
corn = clustered.eq(ee.Number.parse(corn_cluster))
soybean = clustered.eq(ee.Number.parse(soybean_cluster))

# 合并作物图层
# 0 = 其他作物, 1 = 玉米, 2 = 大豆
merged = corn.add(soybean.multiply(2))

# 设置可视化参数
crop_vis = {'min': 0, 'max': 2, 'palette': ['#bdbdbd', '#ffd400', '#267300']}
Map.addLayer(merged.clip(geometry), crop_vis, 'Crop Map (Detected)')

print("已创建作物分布地图")
print("- 灰色: 其他作物")
print("- 黄色: 玉米")
print("- 绿色: 大豆")

# 加载官方CDL作物地图进行比较
cdl_comparison = ee.ImageCollection('USDA/NASS/CDL') \
    .filter(ee.Filter.date(start_date, end_date)) \
    .first()

crop_landcover_comparison = cdl_comparison.select('cropland')
crop_map = crop_landcover_comparison.updateMask(cropland_mask).rename('crops')

# 重新分类CDL数据以匹配我们的分类
# 原始数据中每种作物都有唯一值（0-254）
# 我们重新映射如下：
# 作物     | 源值 | 目标值
# 玉米     | 1    | 1
# 大豆     | 5    | 2
# 其他作物 | 0-255| 0

crop_classes = ee.List.sequence(0, 254)
target_classes = ee.List.repeat(0, 255).set(1, 1).set(5, 2)
crop_map_reclass = crop_map.remap(crop_classes, target_classes).rename('crops')

# 添加官方作物地图到地图
Map.addLayer(crop_map_reclass.clip(geometry), crop_vis, 'Crop Landcover (CDL)')

print("已添加官方CDL作物地图用于比较")
print("\n结果分析:")
print("- 我们的无监督分类结果与官方地图存在一些差异")
print("- 但在没有实地标签的情况下获得了相当不错的结果")
print("- 可以通过后处理步骤进一步改善结果")

print("地图图层说明:")
print("1. Selected Region - 研究区域边界（红色）")
print("2. Crop Mask - 农作物掩码（绿色=耕地，白色=非耕地）")
print("3. Extracted Samples - 训练样本点（蓝色）")
print("4. Clusters - 无监督聚类结果")
print("5. Crop Map (Detected) - 我们检测到的作物地图")
print("6. Crop Landcover (CDL) - 官方CDL作物地图")
print("\n图例:")
print("- 灰色: 其他作物")
print("- 黄色: 玉米")
print("- 绿色: 大豆")
print("\n可以在地图界面中切换不同图层进行比较分析")

# 显示地图
Map