# 全球Satellite Embedding数据下载问题修复指南

## 问题描述

在下载全球Satellite Embedding数据时，出现了数据维度异常的问题：
- **预期结果**: 1440列×720行（全球0.25°分辨率）
- **实际结果**: 1列×720行（数据严重异常）

## 问题原因分析

### 1. 投影转换问题
- Earth Engine在处理全球范围数据时的投影转换可能出现异常
- 从原始投影转换到EPSG:4326时可能丢失经度信息

### 2. 区域定义问题
- 全球边界定义（-180°到180°）可能触发Earth Engine的边界处理bug
- 跨越国际日期变更线的区域处理异常

### 3. 分辨率参数问题
- scale参数与crs_transform参数冲突
- 全球数据的分辨率计算可能不准确

### 4. Earth Engine限制
- 对超大数据集的处理限制
- 内存或计算资源限制导致数据截断

## 解决方案

### 方案1: 修复版下载（推荐尝试）

文件：`download_satellite_embedding_global_fixed.ipynb`

**主要修复**：
- 使用`ee.Geometry.Rectangle`替代`Polygon`
- 排除极地区域（-85°到85°）避免投影问题
- 使用`crs_transform`参数精确控制分辨率
- 添加详细的数据验证

```python
# 修复的关键参数
roi = ee.Geometry.Rectangle([-180, -85, 180, 85])
crs_transform = [0.25, 0, -180, 0, -0.25, 85]
```

### 方案2: 分块下载（强烈推荐）

文件：`download_satellite_embedding_tiles.ipynb`

**策略**：
- 将全球分成6×3=18个区域块
- 每块单独下载，避免大数据集问题
- 后续拼接成完整全球数据

**优势**：
- 避免投影转换问题
- 支持并行下载
- 失败重试更容易
- 数据验证更准确

**分块策略**：
```
经度方向：6块，每块60°
纬度方向：3块，每块约57°
总共：18个区域块
每块大小：约240×227像素
```

### 方案3: 使用Earth Engine Export

如果上述方案仍有问题，建议使用Earth Engine的Export功能：

```python
# 导出到Google Drive
task = ee.batch.Export.image.toDrive(
    image=image,
    description='satellite_embedding_global',
    folder='EE_Downloads',
    region=roi,
    scale=27830,
    crs='EPSG:4326',
    maxPixels=1e13
)
task.start()
```

## 使用建议

### 第1步：诊断测试
运行 `download_satellite_embedding_global_fixed.ipynb` 进行单波段测试

### 第2步：分块下载
如果修复版仍有问题，使用 `download_satellite_embedding_tiles.ipynb`

### 第3步：数据拼接
使用生成的拼接脚本合并分块数据

### 第4步：验证结果
检查最终数据的维度和完整性

## 数据验证方法

### 使用rasterio检查
```python
import rasterio

with rasterio.open('your_file.tif') as src:
    print(f"维度: {src.width}×{src.height}")
    print(f"投影: {src.crs}")
    print(f"边界: {src.bounds}")
    print(f"变换: {src.transform}")
    
    # 检查数据
    data = src.read(1)
    print(f"数据形状: {data.shape}")
    print(f"数据范围: {data.min():.4f} - {data.max():.4f}")
```

### 预期结果
- **维度**: 1440×720（完整全球）或1440×680（排除极地）
- **投影**: EPSG:4326
- **分辨率**: 0.25°
- **边界**: 经度-180到180，纬度-90到90（或-85到85）

## 故障排除

### 如果仍然是1列×720行
1. **检查Earth Engine版本**：更新到最新版本
2. **尝试不同的投影**：使用Web Mercator (EPSG:3857)
3. **减小数据范围**：先下载半球数据测试
4. **联系技术支持**：这可能是Earth Engine的bug

### 如果下载超时
1. **减小区域范围**：使用更小的分块
2. **降低分辨率**：先用0.5°测试
3. **分时段下载**：避开高峰期

### 如果内存不足
1. **增加系统内存**：推荐16GB以上
2. **使用SSD存储**：提高I/O性能
3. **关闭其他程序**：释放系统资源

## 技术细节

### 投影变换矩阵
```python
# 0.25度分辨率的变换矩阵
# [像素宽度, 旋转, 左上角X, 旋转, 像素高度(负值), 左上角Y]
crs_transform = [0.25, 0, -180, 0, -0.25, 85]
```

### 分块索引
```python
# 块命名规则：tile_XX_YY
# XX: 经度索引 (00-05)
# YY: 纬度索引 (00-02)
tile_id = f"tile_{lon_idx:02d}_{lat_idx:02d}"
```

## 性能优化

### 下载优化
- 使用有线网络连接
- 设置合理的重试间隔
- 监控系统资源使用

### 存储优化
- 使用压缩格式（LZW压缩）
- 定期清理临时文件
- 备份重要数据

## 联系支持

如果问题仍然存在：
1. 收集详细的错误日志
2. 记录系统环境信息
3. 提供问题重现步骤
4. 联系Earth Engine技术支持

## 更新日志

- v1.0: 识别1列×720行问题
- v1.1: 创建修复版下载方案
- v1.2: 实现分块下载策略
- v1.3: 添加数据拼接功能
