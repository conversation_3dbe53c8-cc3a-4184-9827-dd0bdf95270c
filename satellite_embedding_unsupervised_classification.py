"""
使用卫星嵌入数据集进行无监督分类 - 简化版本
基于Google Earth Engine教程修改而来

本脚本演示如何：
1. 加载卫星嵌入数据集
2. 对研究区所有像元进行无监督聚类分析
3. 可视化聚类结果
"""

import ee
import geemap

# 初始化Earth Engine
ee.Initialize()

# 创建交互式地图
Map = geemap.Map()

print("=== 使用卫星嵌入数据集进行无监督分类 ===")

# 选择区域 - 爱荷华州Cerro Gordo County
print("\n1. 选择研究区域")
counties = ee.FeatureCollection('TIGER/2018/Counties')
selected = counties.filter(ee.Filter.eq('GEOID', '19033'))
geometry = selected.geometry()

Map.centerObject(geometry, 12)
Map.addLayer(geometry, {'color': 'red'}, 'Selected Region', False)
print("已选择爱荷华州Cerro Gordo County作为研究区域")

# 准备卫星嵌入数据集
print("\n2. 准备卫星嵌入数据集")
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

year = 2022
start_date = ee.Date.fromYMD(year, 1, 1)
end_date = start_date.advance(1, 'year')

filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

embeddings_image = filtered_embeddings.mosaic()
print(f"已加载{year}年的卫星嵌入数据")

# 准备聚类图像（使用完整的嵌入数据，不应用掩码）
print("\n3. 准备聚类图像")
cluster_image = embeddings_image
print("已准备聚类图像（包含研究区所有像元）")

# 提取训练样本
print("\n4. 提取训练样本")

training = cluster_image.sample(**{
    'region': geometry,
    'scale': 10,
    'numPixels': 1000,
    'seed': 100,
    'dropNulls': True,
    'geometries': True
})

Map.addLayer(training, {'color': 'blue'}, 'Extracted Samples', False)
print("已完成随机采样，提取1000个训练样本")

# 执行无监督聚类
print("\n5. 执行无监督聚类")
min_clusters = 4
max_clusters = 5

clusterer = ee.Clusterer.wekaCascadeKMeans(**{
    'minClusters': min_clusters, 
    'maxClusters': max_clusters
}).train(**{
    'features': training,
    'inputProperties': cluster_image.bandNames()
})

clustered = cluster_image.cluster(clusterer)
print(f"已完成聚类分析（{min_clusters}-{max_clusters}个聚类）")

# 可视化聚类结果
print("\n6. 可视化聚类结果")
# 直接显示聚类结果，不进行标签分配
Map.addLayer(clustered.randomVisualizer().clip(geometry), {}, 'Unsupervised Clusters')
print("已添加无监督聚类结果到地图")

print("\n=== 无监督分类完成 ===")
print("地图图层说明：")
print("1. Selected Region - 研究区域边界")
print("2. Extracted Samples - 训练样本点")
print("3. Unsupervised Clusters - 无监督聚类结果")
print("\n说明：聚类结果显示了研究区内不同地物类型的空间分布")

# 返回地图对象
Map
