"""
测试脚本：验证卫星嵌入数据集代码的基本功能
"""

import sys

def test_imports():
    """测试必要库的导入"""
    try:
        import ee
        print("✓ earthengine-api 导入成功")
        
        import geemap
        print("✓ geemap 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        print("请安装必要的包：")
        print("conda install -c conda-forge earthengine-api geemap")
        return False

def test_ee_initialization():
    """测试Earth Engine初始化"""
    try:
        import ee
        # 尝试初始化（可能需要认证）
        ee.Initialize()
        print("✓ Earth Engine 初始化成功")
        return True
    except Exception as e:
        print(f"✗ Earth Engine 初始化失败: {e}")
        print("可能需要先进行认证：")
        print("import ee; ee.Authenticate()")
        return False

def test_data_access():
    """测试数据集访问"""
    try:
        import ee
        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
        print("✓ 卫星嵌入数据集访问成功")
        
        # 测试基本信息获取
        size = embeddings.size()
        print(f"✓ 数据集大小获取成功")
        
        return True
    except Exception as e:
        print(f"✗ 数据集访问失败: {e}")
        return False

def test_geometry_creation():
    """测试几何对象创建"""
    try:
        import ee
        geometry = ee.Geometry.Polygon([[
            [76.3978, 12.5521],
            [76.3978, 12.3550],
            [76.6519, 12.3550],
            [76.6519, 12.5521]
        ]])
        print("✓ 几何对象创建成功")
        return True
    except Exception as e:
        print(f"✗ 几何对象创建失败: {e}")
        return False

def test_date_operations():
    """测试日期操作"""
    try:
        import ee
        year = 2024
        start_date = ee.Date.fromYMD(year, 1, 1)
        end_date = start_date.advance(1, 'year')
        print("✓ 日期操作成功")
        return True
    except Exception as e:
        print(f"✗ 日期操作失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 卫星嵌入数据集代码测试 ===\n")
    
    tests = [
        ("导入测试", test_imports),
        ("Earth Engine初始化测试", test_ee_initialization),
        ("数据集访问测试", test_data_access),
        ("几何对象测试", test_geometry_creation),
        ("日期操作测试", test_date_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！代码可以正常运行。")
        print("\n下一步：")
        print("1. 运行 satellite_embedding_introduction.py")
        print("2. 或在Jupyter中打开 satellite_embedding_introduction.ipynb")
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
        print("\n建议：")
        print("1. 确保已安装 earthengine-api 和 geemap")
        print("2. 进行 Earth Engine 认证")
        print("3. 检查网络连接")

if __name__ == "__main__":
    main()
