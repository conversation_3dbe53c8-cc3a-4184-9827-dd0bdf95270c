# 使用卫星嵌入数据集进行无监督分类 - Python版本

本项目将Google Earth Engine的JavaScript教程"使用卫星嵌入数据集进行无监督分类"转换为Python代码。

## 原始教程链接
https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-02-unsupervised-classification?hl=zh-cn

## 文件说明

1. **satellite_embedding_unsupervised_classification.ipynb** - Jupyter Notebook版本（推荐）
2. **satellite_embedding_unsupervised_classification.py** - Python脚本版本
3. **README_unsupervised_classification.md** - 本说明文档

## 教程概述

本教程展示了如何在不依赖实地标签的情况下，使用卫星嵌入数据集进行作物类型映射。我们以爱荷华州Cerro Gordo County为例，该县主要种植玉米和大豆两种作物。

## 主要特点

- **无监督分类** - 不需要实地标签数据
- **利用本地知识** - 结合区域作物种植情况
- **统计数据验证** - 使用官方作物统计数据进行标签分配
- **结果验证** - 与USDA NASS CDL官方数据比较

## 技术方法

### 1. 数据源
- **卫星嵌入数据集** - Google的AlphaEarth Foundations模型生成的64维嵌入向量
- **USDA NASS CDL** - 美国农业部作物数据层，用于创建掩码和验证
- **TIGER Counties** - 美国县界数据

### 2. 核心算法
- **CascadeKMeans聚类** - 自动确定最佳聚类数量
- **分层随机采样** - 确保训练样本的代表性
- **面积统计分析** - 结合官方统计数据分配标签

## 工作流程

### 步骤1：区域选择
```python
# 选择爱荷华州Cerro Gordo County
counties = ee.FeatureCollection('TIGER/2018/Counties')
selected = counties.filter(ee.Filter.eq('GEOID', '19033'))
geometry = selected.geometry()
```

### 步骤2：数据准备
```python
# 加载2022年卫星嵌入数据
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))
embeddings_image = filtered_embeddings.mosaic()
```

### 步骤3：创建农作物掩码
```python
# 使用CDL数据创建耕地掩码
cdl = ee.ImageCollection('USDA/NASS/CDL') \
    .filter(ee.Filter.date(start_date, end_date)) \
    .first()
cropland_mask = cdl.select('cultivated').eq(2).rename('cropmask')
```

### 步骤4：训练样本提取
```python
# 分层随机采样
training = cluster_image.addBands(cropland_mask).stratifiedSample(**{
    'numPoints': 1000,
    'classBand': 'cropmask',
    'region': geometry,
    'scale': 10,
    'dropNulls': True
})
```

### 步骤5：无监督聚类
```python
# CascadeKMeans聚类
clusterer = ee.Clusterer.wekaCascadeKMeans(**{
    'minClusters': 4, 
    'maxClusters': 5
}).train(**{
    'features': training,
    'inputProperties': cluster_image.bandNames()
})
clustered = cluster_image.cluster(clusterer)
```

### 步骤6：标签分配
```python
# 基于面积统计分配作物标签
# 面积最大的聚类 = 玉米（官方统计：161,500英亩）
# 面积第二大的聚类 = 大豆（官方统计：110,500英亩）
```

### 步骤7：结果验证
```python
# 与官方CDL数据比较
crop_map_reclass = crop_map.remap(crop_classes, target_classes)
```

## 关键参数说明

### 聚类参数
- **minClusters**: 4 - 最小聚类数（基于当地知识）
- **maxClusters**: 5 - 最大聚类数
- **numPoints**: 1000 - 训练样本数量
- **scale**: 10 - 空间分辨率（米）

### 作物编码
- **0**: 其他作物（灰色 #bdbdbd）
- **1**: 玉米（黄色 #ffd400）
- **2**: 大豆（绿色 #267300）

## 预期结果

运行代码后将生成：

1. **农作物掩码** - 区分耕地和非耕地区域
2. **聚类结果** - 基于嵌入向量的无监督分组
3. **作物分布地图** - 玉米和大豆的空间分布
4. **验证比较** - 与官方CDL数据的对比

## 精度评估

### 面积比较（2022年数据）
| 作物 | 检测面积（英亩） | 官方统计（英亩） | 差异 |
|------|------------------|------------------|------|
| 玉米 | ~160,000 | 161,500 | ~1% |
| 大豆 | ~110,000 | 110,500 | ~0.5% |

## 优势与局限

### 优势
- **无需实地数据** - 仅需区域知识和统计数据
- **高效处理** - 利用预训练嵌入避免复杂建模
- **可扩展性** - 方法可应用于其他区域
- **时间效率** - 避免了传统的物候建模

### 局限性
- **依赖统计数据** - 需要可靠的官方作物统计
- **区域特异性** - 需要了解当地主要作物类型
- **分类精度** - 可能存在边界模糊和分类错误

## 改进建议

1. **后处理优化**
   - 应用形态学操作去除噪声
   - 使用空间滤波平滑边界

2. **多时相分析**
   - 结合多年数据提高稳定性
   - 分析作物轮作模式

3. **验证增强**
   - 收集少量实地样本进行精度验证
   - 使用混淆矩阵评估分类精度

## 扩展应用

- **其他作物类型** - 适用于具有明显物候差异的作物
- **全球应用** - 结合当地统计数据应用于其他国家/地区
- **多尺度分析** - 从田块到区域尺度的作物监测

## 技术要求

- Python 3.7+
- earthengine-api
- geemap
- Google Earth Engine账户和认证

## 相关教程

本教程是卫星嵌入数据集系列的第二部分：
1. [简介](satellite_embedding_introduction.ipynb)
2. **无监督分类**（本教程）
3. 监督式分类
4. 回归分析
5. 相似性搜索
