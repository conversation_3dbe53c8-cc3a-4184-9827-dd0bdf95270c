# 导入必要的库
import ee
import geemap
import os
from pathlib import Path
import time

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")
print("开始准备下载Satellite Embedding数据")

# 设置参数
print("=== 设置下载参数 ===")

# 目标路径
base_path = r"H:\satellite_embedding\global_025"

# 年份范围
start_year = 2017
end_year = 2024
years = list(range(start_year, end_year + 1))

# 波段列表（A00到A63，共64个波段）
bands = [f'A{i:02d}' for i in range(64)]

# 研究区域：全球
roi = ee.Geometry.Polygon([[
    [-180, -90],   # 西南角
    [-180, 90],    # 西北角
    [180, 90],     # 东北角
    [180, -90]     # 东南角
]])

# 下载参数
crs = "EPSG:4326"
scale = 27830  # 约0.25度分辨率

print(f"目标路径: {base_path}")
print(f"年份范围: {start_year}-{end_year} ({len(years)}年)")
print(f"波段数量: {len(bands)}个")
print(f"研究区域: 全球 (-180°-180°E, -90°-90°N)")
print(f"分辨率: 0.25° (scale={scale})")
print(f"投影: {crs}")

# 创建基础目录
Path(base_path).mkdir(parents=True, exist_ok=True)
print(f"已创建基础目录: {base_path}")

# 创建年份目录
print("\n=== 创建年份目录 ===")

year_paths = {}
for year in years:
    year_path = os.path.join(base_path, str(year))
    Path(year_path).mkdir(parents=True, exist_ok=True)
    year_paths[year] = year_path
    print(f"已创建目录: {year_path}")

print(f"\n共创建 {len(year_paths)} 个年份目录")

# 定义下载函数
def download_band_for_year(year, band, roi, output_path, crs, scale):
    """
    下载指定年份和波段的satellite embedding数据
    
    参数:
        year: 年份
        band: 波段名称 (如 'A00')
        roi: 研究区域
        output_path: 输出文件路径
        crs: 坐标系
        scale: 分辨率
    """
    try:
        # 加载satellite embedding数据集
        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
        
        # 设置时间范围
        start_date = ee.Date.fromYMD(year, 1, 1)
        end_date = start_date.advance(1, 'year')
        
        # 过滤数据
        filtered = embeddings \
            .filter(ee.Filter.date(start_date, end_date)) \
            .filter(ee.Filter.bounds(roi))
        
        # 创建镶嵌图并选择指定波段
        image = filtered.mosaic().select([band])
        
        # 下载图像
        geemap.download_ee_image(
            image=image,
            filename=output_path,
            region=roi,
            crs=crs,
            scale=scale,
        )
        
        return True, None
        
    except Exception as e:
        return False, str(e)

print("下载函数已定义")

# 执行批量下载
print("\n=== 开始批量下载 ===")

total_files = len(years) * len(bands)
completed = 0
failed = 0
failed_list = []

print(f"总计需要下载: {total_files} 个文件")
print(f"预计时间: 约 {total_files * 0.5:.1f} 分钟")
print("\n开始下载...")

start_time = time.time()

for year in years:
    print(f"\n--- 处理 {year} 年 ---")
    year_path = year_paths[year]
    
    for i, band in enumerate(bands):
        # 构建输出文件路径
        filename = f"{band}_{year}.tif"
        output_path = os.path.join(year_path, filename)
        
        # 检查文件是否已存在
        if os.path.exists(output_path):
            print(f"  跳过 {band} (文件已存在)")
            completed += 1
            continue
        
        print(f"  下载 {band} -> {filename}")
        
        # 执行下载
        success, error = download_band_for_year(
            year, band, roi, output_path, crs, scale
        )
        
        if success:
            completed += 1
            print(f"    ✓ 成功 ({completed}/{total_files})")
        else:
            failed += 1
            failed_list.append((year, band, error))
            print(f"    ✗ 失败: {error}")
        
        # 显示进度
        if (i + 1) % 10 == 0:
            elapsed = time.time() - start_time
            progress = completed / total_files * 100
            print(f"  进度: {progress:.1f}% (已用时 {elapsed/60:.1f} 分钟)")
        
        # 短暂延迟避免请求过快
        time.sleep(1)

# 下载完成统计
end_time = time.time()
total_time = end_time - start_time

print(f"\n=== 下载完成 ===")
print(f"总用时: {total_time/60:.1f} 分钟")
print(f"成功下载: {completed} 个文件")
print(f"下载失败: {failed} 个文件")
print(f"成功率: {completed/total_files*100:.1f}%")

# 显示失败的下载
if failed_list:
    print(f"\n=== 失败的下载 ===")
    for year, band, error in failed_list:
        print(f"  {year}/{band}: {error}")
    
    print(f"\n可以重新运行此notebook来重试失败的下载")
else:
    print(f"\n🎉 所有文件下载成功！")

print(f"\n数据保存位置: {base_path}")

# 验证下载结果
print("\n=== 验证下载结果 ===")

total_expected = len(years) * len(bands)
total_found = 0
missing_files = []

for year in years:
    year_path = year_paths[year]
    year_files = 0
    
    for band in bands:
        filename = f"{band}_{year}.tif"
        file_path = os.path.join(year_path, filename)
        
        if os.path.exists(file_path):
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > 1000:  # 文件大小大于1KB认为有效
                year_files += 1
                total_found += 1
            else:
                missing_files.append((year, band, "文件过小"))
        else:
            missing_files.append((year, band, "文件不存在"))
    
    print(f"{year}年: {year_files}/{len(bands)} 个文件")

print(f"\n总计: {total_found}/{total_expected} 个文件")
print(f"完整性: {total_found/total_expected*100:.1f}%")

if missing_files:
    print(f"\n缺失文件 ({len(missing_files)}个):")
    for year, band, reason in missing_files[:10]:  # 只显示前10个
        print(f"  {year}/{band}: {reason}")
    if len(missing_files) > 10:
        print(f"  ... 还有 {len(missing_files)-10} 个文件")
else:
    print("\n✅ 所有文件下载完整！")

# 生成下载报告
print("\n=== 下载报告 ===")

report_path = os.path.join(base_path, "download_report.txt")

with open(report_path, 'w', encoding='utf-8') as f:
    f.write("Satellite Embedding 0.25° 数据下载报告\n")
    f.write("=" * 50 + "\n\n")
    
    f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"总用时: {total_time/60:.1f} 分钟\n")
    f.write(f"研究区域: 丝绸之路流域 (60°-120°E, 25°-50°N)\n")
    f.write(f"年份范围: {start_year}-{end_year}\n")
    f.write(f"分辨率: 0.25° (scale={scale})\n")
    f.write(f"投影: {crs}\n\n")
    
    f.write(f"下载统计:\n")
    f.write(f"- 预期文件数: {total_expected}\n")
    f.write(f"- 成功下载: {completed}\n")
    f.write(f"- 下载失败: {failed}\n")
    f.write(f"- 实际文件数: {total_found}\n")
    f.write(f"- 完整性: {total_found/total_expected*100:.1f}%\n\n")
    
    if failed_list:
        f.write(f"失败列表:\n")
        for year, band, error in failed_list:
            f.write(f"- {year}/{band}: {error}\n")
        f.write("\n")
    
    if missing_files:
        f.write(f"缺失文件:\n")
        for year, band, reason in missing_files:
            f.write(f"- {year}/{band}: {reason}\n")
    
    f.write("\n数据组织结构:\n")
    for year in years:
        year_path = year_paths[year]
        year_files = len([f for f in os.listdir(year_path) if f.endswith('.tif')])
        f.write(f"- {year}/: {year_files} 个TIF文件\n")

print(f"下载报告已保存: {report_path}")
print(f"\n=== 下载任务完成 ===")