{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 下载全球逐年逐波段Satellite Embedding 0.25°数据\n", "\n", "## 目标\n", "- 下载全球的Satellite Embedding V1数据\n", "- 按年份组织：每年一个文件夹\n", "- 按波段组织：每个波段一个TIF文件\n", "- 重采样到0.25°分辨率\n", "- 保存到H:\\satellite_embedding\\global_025路径\n", "\n", "## 数据组织结构\n", "```\n", "H:\\satellite_embedding\\global_025\\\n", "├── 2017\\\n", "│   ├── A00_2017.tif\n", "│   ├── A01_2017.tif\n", "│   ├── ...\n", "│   └── A63_2017.tif\n", "├── 2018\\\n", "│   ├── A00_2018.tif\n", "│   └── ...\n", "└── ...\n", "```\n", "\n", "## 注意事项\n", "- **数据量巨大**: 全球数据每个文件可能达到几百MB到几GB\n", "- **下载时间长**: 预计需要数天到数周时间\n", "- **存储空间**: 建议预留至少500GB-1TB空间\n", "- **网络稳定性**: 建议在稳定网络环境下进行\n", "- **分批下载**: 可以考虑分年份或分区域下载"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n", "开始准备下载Satellite Embedding数据\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import os\n", "from pathlib import Path\n", "import time\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"开始准备下载Satellite Embedding数据\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置参数\n", "print(\"=== 设置下载参数 ===\")\n", "\n", "# 目标路径\n", "base_path = r\"H:\\satellite_embedding\\global_025\"\n", "\n", "# 年份范围\n", "start_year = 2017\n", "end_year = 2024\n", "years = list(range(start_year, end_year + 1))\n", "\n", "# 波段列表（A00到A63，共64个波段）\n", "bands = [f'A{i:02d}' for i in range(64)]\n", "\n", "# 研究区域：全球\n", "roi = ee.Geometry.Polygon([[\n", "    [-180, -90],   # 西南角\n", "    [-180, 90],    # 西北角\n", "    [180, 90],     # 东北角\n", "    [180, -90]     # 东南角\n", "]])\n", "\n", "# 下载参数\n", "crs = \"EPSG:4326\"\n", "scale = 27830  # 约0.25度分辨率\n", "\n", "print(f\"目标路径: {base_path}\")\n", "print(f\"年份范围: {start_year}-{end_year} ({len(years)}年)\")\n", "print(f\"波段数量: {len(bands)}个\")\n", "print(f\"研究区域: 全球 (-180°-180°E, -90°-90°N)\")\n", "print(f\"分辨率: 0.25° (scale={scale})\")\n", "print(f\"投影: {crs}\")\n", "\n", "# 创建基础目录\n", "Path(base_path).mkdir(parents=True, exist_ok=True)\n", "print(f\"已创建基础目录: {base_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建年份目录\n", "print(\"\\n=== 创建年份目录 ===\")\n", "\n", "year_paths = {}\n", "for year in years:\n", "    year_path = os.path.join(base_path, str(year))\n", "    Path(year_path).mkdir(parents=True, exist_ok=True)\n", "    year_paths[year] = year_path\n", "    print(f\"已创建目录: {year_path}\")\n", "\n", "print(f\"\\n共创建 {len(year_paths)} 个年份目录\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义下载函数\n", "def download_band_for_year(year, band, roi, output_path, crs, scale):\n", "    \"\"\"\n", "    下载指定年份和波段的satellite embedding数据\n", "    \n", "    参数:\n", "        year: 年份\n", "        band: 波段名称 (如 'A00')\n", "        roi: 研究区域\n", "        output_path: 输出文件路径\n", "        crs: 坐标系\n", "        scale: 分辨率\n", "    \"\"\"\n", "    try:\n", "        # 加载satellite embedding数据集\n", "        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "        \n", "        # 设置时间范围\n", "        start_date = ee.Date.fromYMD(year, 1, 1)\n", "        end_date = start_date.advance(1, 'year')\n", "        \n", "        # 过滤数据\n", "        filtered = embeddings \\\n", "            .filter(ee.Filter.date(start_date, end_date)) \\\n", "            .filter(ee.Filter.bounds(roi))\n", "        \n", "        # 创建镶嵌图并选择指定波段\n", "        image = filtered.mosaic().select([band])\n", "        \n", "        # 下载图像\n", "        geemap.download_ee_image(\n", "            image=image,\n", "            filename=output_path,\n", "            region=roi,\n", "            crs=crs,\n", "            scale=scale,\n", "        )\n", "        \n", "        return True, None\n", "        \n", "    except Exception as e:\n", "        return False, str(e)\n", "\n", "print(\"下载函数已定义\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行批量下载\n", "print(\"\\n=== 开始批量下载 ===\")\n", "\n", "total_files = len(years) * len(bands)\n", "completed = 0\n", "failed = 0\n", "failed_list = []\n", "\n", "print(f\"总计需要下载: {total_files} 个文件\")\n", "print(f\"预计时间: 约 {total_files * 0.5:.1f} 分钟\")\n", "print(\"\\n开始下载...\")\n", "\n", "start_time = time.time()\n", "\n", "for year in years:\n", "    print(f\"\\n--- 处理 {year} 年 ---\")\n", "    year_path = year_paths[year]\n", "    \n", "    for i, band in enumerate(bands):\n", "        # 构建输出文件路径\n", "        filename = f\"{band}_{year}.tif\"\n", "        output_path = os.path.join(year_path, filename)\n", "        \n", "        # 检查文件是否已存在\n", "        if os.path.exists(output_path):\n", "            print(f\"  跳过 {band} (文件已存在)\")\n", "            completed += 1\n", "            continue\n", "        \n", "        print(f\"  下载 {band} -> {filename}\")\n", "        \n", "        # 执行下载\n", "        success, error = download_band_for_year(\n", "            year, band, roi, output_path, crs, scale\n", "        )\n", "        \n", "        if success:\n", "            completed += 1\n", "            print(f\"    ✓ 成功 ({completed}/{total_files})\")\n", "        else:\n", "            failed += 1\n", "            failed_list.append((year, band, error))\n", "            print(f\"    ✗ 失败: {error}\")\n", "        \n", "        # 显示进度\n", "        if (i + 1) % 10 == 0:\n", "            elapsed = time.time() - start_time\n", "            progress = completed / total_files * 100\n", "            print(f\"  进度: {progress:.1f}% (已用时 {elapsed/60:.1f} 分钟)\")\n", "        \n", "        # 短暂延迟避免请求过快\n", "        time.sleep(1)\n", "\n", "# 下载完成统计\n", "end_time = time.time()\n", "total_time = end_time - start_time\n", "\n", "print(f\"\\n=== 下载完成 ===\")\n", "print(f\"总用时: {total_time/60:.1f} 分钟\")\n", "print(f\"成功下载: {completed} 个文件\")\n", "print(f\"下载失败: {failed} 个文件\")\n", "print(f\"成功率: {completed/total_files*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示失败的下载\n", "if failed_list:\n", "    print(f\"\\n=== 失败的下载 ===\")\n", "    for year, band, error in failed_list:\n", "        print(f\"  {year}/{band}: {error}\")\n", "    \n", "    print(f\"\\n可以重新运行此notebook来重试失败的下载\")\n", "else:\n", "    print(f\"\\n🎉 所有文件下载成功！\")\n", "\n", "print(f\"\\n数据保存位置: {base_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 验证下载结果\n", "print(\"\\n=== 验证下载结果 ===\")\n", "\n", "total_expected = len(years) * len(bands)\n", "total_found = 0\n", "missing_files = []\n", "\n", "for year in years:\n", "    year_path = year_paths[year]\n", "    year_files = 0\n", "    \n", "    for band in bands:\n", "        filename = f\"{band}_{year}.tif\"\n", "        file_path = os.path.join(year_path, filename)\n", "        \n", "        if os.path.exists(file_path):\n", "            # 检查文件大小\n", "            file_size = os.path.getsize(file_path)\n", "            if file_size > 1000:  # 文件大小大于1KB认为有效\n", "                year_files += 1\n", "                total_found += 1\n", "            else:\n", "                missing_files.append((year, band, \"文件过小\"))\n", "        else:\n", "            missing_files.append((year, band, \"文件不存在\"))\n", "    \n", "    print(f\"{year}年: {year_files}/{len(bands)} 个文件\")\n", "\n", "print(f\"\\n总计: {total_found}/{total_expected} 个文件\")\n", "print(f\"完整性: {total_found/total_expected*100:.1f}%\")\n", "\n", "if missing_files:\n", "    print(f\"\\n缺失文件 ({len(missing_files)}个):\")\n", "    for year, band, reason in missing_files[:10]:  # 只显示前10个\n", "        print(f\"  {year}/{band}: {reason}\")\n", "    if len(missing_files) > 10:\n", "        print(f\"  ... 还有 {len(missing_files)-10} 个文件\")\n", "else:\n", "    print(\"\\n✅ 所有文件下载完整！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成下载报告\n", "print(\"\\n=== 下载报告 ===\")\n", "\n", "report_path = os.path.join(base_path, \"download_report.txt\")\n", "\n", "with open(report_path, 'w', encoding='utf-8') as f:\n", "    f.write(\"Satellite Embedding 0.25° 数据下载报告\\n\")\n", "    f.write(\"=\" * 50 + \"\\n\\n\")\n", "    \n", "    f.write(f\"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "    f.write(f\"总用时: {total_time/60:.1f} 分钟\\n\")\n", "    f.write(f\"研究区域: 丝绸之路流域 (60°-120°E, 25°-50°N)\\n\")\n", "    f.write(f\"年份范围: {start_year}-{end_year}\\n\")\n", "    f.write(f\"分辨率: 0.25° (scale={scale})\\n\")\n", "    f.write(f\"投影: {crs}\\n\\n\")\n", "    \n", "    f.write(f\"下载统计:\\n\")\n", "    f.write(f\"- 预期文件数: {total_expected}\\n\")\n", "    f.write(f\"- 成功下载: {completed}\\n\")\n", "    f.write(f\"- 下载失败: {failed}\\n\")\n", "    f.write(f\"- 实际文件数: {total_found}\\n\")\n", "    f.write(f\"- 完整性: {total_found/total_expected*100:.1f}%\\n\\n\")\n", "    \n", "    if failed_list:\n", "        f.write(f\"失败列表:\\n\")\n", "        for year, band, error in failed_list:\n", "            f.write(f\"- {year}/{band}: {error}\\n\")\n", "        f.write(\"\\n\")\n", "    \n", "    if missing_files:\n", "        f.write(f\"缺失文件:\\n\")\n", "        for year, band, reason in missing_files:\n", "            f.write(f\"- {year}/{band}: {reason}\\n\")\n", "    \n", "    f.write(\"\\n数据组织结构:\\n\")\n", "    for year in years:\n", "        year_path = year_paths[year]\n", "        year_files = len([f for f in os.listdir(year_path) if f.endswith('.tif')])\n", "        f.write(f\"- {year}/: {year_files} 个TIF文件\\n\")\n", "\n", "print(f\"下载报告已保存: {report_path}\")\n", "print(f\"\\n=== 下载任务完成 ===\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}