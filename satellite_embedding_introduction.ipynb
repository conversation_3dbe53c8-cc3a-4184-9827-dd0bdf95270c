{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 卫星嵌入数据集简介 - Python版本\n", "\n", "基于Google Earth Engine教程转换而来\n", "https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-01-introduction?hl=zh-cn\n", "\n", "本教程演示如何：\n", "1. 访问和使用Google的卫星嵌入数据集\n", "2. 对嵌入数据进行可视化\n", "3. 使用无监督聚类分析嵌入数据\n", "\n", "## 什么是嵌入？\n", "\n", "嵌入是一种将大量信息压缩为一组较小的特征的方法，这些特征代表有意义的语义。AlphaEarth Foundations模型会获取来自传感器（包括Sentinel-2、Sentinel-1和Landsat）的时间序列图像，并学习如何仅使用64个数字来唯一表示来源和目标之间的互信息。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图已创建，卫星嵌入数据集已加载\n"]}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "\n", "# 访问卫星嵌入数据集\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "# 设置地图为卫星底图\n", "Map.add_basemap('SATELLITE')\n", "\n", "print(\"地图已创建，卫星嵌入数据集已加载\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["感兴趣区域已定义（印度KRS水库附近）\n"]}], "source": ["# 定义感兴趣的区域 - 印度克里希纳拉贾萨加拉(KRS)水库附近\n", "geometry = ee.Geometry.Polygon([[\n", "    [76.3978, 12.5521],\n", "    [76.3978, 12.3550],\n", "    [76.6519, 12.3550],\n", "    [76.6519, 12.5521]\n", "]])\n", "\n", "# 将地图中心设置到感兴趣区域\n", "Map.centerObject(geometry, 12)\n", "\n", "print(\"感兴趣区域已定义（印度KRS水库附近）\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["数据过滤设置：\n", "- 目标年份：2024\n", "- 开始日期：2024-01-01\n", "- 结束日期：2025-01-01\n", "- 已应用时间过滤器\n", "- 已应用空间过滤器\n"]}], "source": ["# 准备卫星嵌入数据集\n", "# 设置年份和日期范围\n", "year = 2024\n", "start_date = ee.Date.fromYMD(year, 1, 1)\n", "end_date = start_date.advance(1, 'year')\n", "\n", "print(f\"数据过滤设置：\")\n", "print(f\"- 目标年份：{year}\")\n", "print(f\"- 开始日期：{year}-01-01\")\n", "print(f\"- 结束日期：{year+1}-01-01\")\n", "\n", "# 应用时间和空间过滤\n", "filtered_embeddings = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "print(\"- 已应用时间过滤器\")\n", "print(\"- 已应用空间过滤器\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["数据处理：\n", "- 已将过滤后的图像集合镶嵌为单一图像\n", "\n", "卫星嵌入图像信息：\n", "- 波段数量：64个（A00到A63）\n", "- 每个波段代表嵌入空间的一个维度\n", "- 所有64个波段共同构成完整的嵌入向量\n"]}], "source": ["# 将多个图块合并为一张图片\n", "embeddings_image = filtered_embeddings.mosaic()\n", "\n", "print(\"数据处理：\")\n", "print(\"- 已将过滤后的图像集合镶嵌为单一图像\")\n", "print(\"\\n卫星嵌入图像信息：\")\n", "print(\"- 波段数量：64个（A00到A63）\")\n", "print(\"- 每个波段代表嵌入空间的一个维度\")\n", "print(\"- 所有64个波段共同构成完整的嵌入向量\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化嵌入数据\n", "\n", "由于我们的图片包含64个波段，无法轻松直观地呈现所有波段中包含的所有信息。我们可以选择任意三个波段，将嵌入空间的三个轴可视化为RGB图像。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已添加嵌入图像可视化图层（使用波段A01, A16, A09作为RGB）\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cff3140b4dbe4b59bcbdb92d0c9d9a55", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[12.453567183649733, 76.52485000000064], controls=(WidgetControl(options=['position', 'transparent_…"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 选择三个波段进行RGB可视化\n", "vis_params = {\n", "    'min': -0.3, \n", "    'max': 0.3, \n", "    'bands': ['A01', 'A16', 'A09']\n", "}\n", "\n", "# 添加嵌入图像到地图\n", "Map.addLayer(\n", "    embeddings_image.clip(geometry), \n", "    vis_params, \n", "    'Embeddings Image'\n", ")\n", "\n", "print(\"已添加嵌入图像可视化图层（使用波段A01, A16, A09作为RGB）\")\n", "\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 无监督聚类分析\n", "\n", "另一种直观呈现此信息的方式是，使用它来对具有相似嵌入的像素进行分组，并使用这些分组来了解模型如何学习景观的空间和时间可变性。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["采样像素数量：1000\n", "已完成像素采样，用于训练聚类模型\n"]}], "source": ["# 设置采样参数\n", "n_samples = 1000\n", "print(f\"采样像素数量：{n_samples}\")\n", "\n", "# 从嵌入图像中采样像素用于训练聚类器\n", "training = embeddings_image.sample(**{\n", "    'region': geometry,\n", "    'scale': 10,\n", "    'numPixels': n_samples,\n", "    'seed': 100\n", "})\n", "\n", "print(\"已完成像素采样，用于训练聚类模型\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["聚类函数已定义\n"]}], "source": ["# 定义聚类函数\n", "def get_clusters(n_clusters):\n", "    \"\"\"\n", "    训练K-means聚类器并对图像进行聚类\n", "    \n", "    参数:\n", "        n_clusters: 聚类数量\n", "    \n", "    返回:\n", "        聚类后的图像\n", "    \"\"\"\n", "    # 创建并训练K-means聚类器\n", "    clusterer = ee.Clusterer.wekaKMeans(n_clusters).train(training)\n", "    \n", "    # 对图像进行聚类\n", "    clustered = embeddings_image.cluster(clusterer)\n", "    return clustered\n", "\n", "print(\"聚类函数已定义\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["--- 生成3个聚类 ---\n", "已添加3聚类结果到地图\n", "\n", "--- 生成5个聚类 ---\n", "已添加5聚类结果到地图\n", "\n", "--- 生成10个聚类 ---\n", "已添加10聚类结果到地图\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cff3140b4dbe4b59bcbdb92d0c9d9a55", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=488047.0, center=[12.446299090250921, 76.55015945434572], controls=(WidgetControl(options=['positio…"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 生成不同数量的聚类结果\n", "print(\"--- 生成3个聚类 ---\")\n", "cluster3 = get_clusters(3)\n", "Map.addLayer(\n", "    cluster3.randomVisualizer().clip(geometry), \n", "    {}, \n", "    '3 clusters'\n", ")\n", "print(\"已添加3聚类结果到地图\")\n", "\n", "print(\"\\n--- 生成5个聚类 ---\")\n", "cluster5 = get_clusters(5)\n", "Map.addLayer(\n", "    cluster5.randomVisualizer().clip(geometry), \n", "    {}, \n", "    '5 clusters'\n", ")\n", "print(\"已添加5聚类结果到地图\")\n", "\n", "print(\"\\n--- 生成10个聚类 ---\")\n", "cluster10 = get_clusters(10)\n", "Map.addLayer(\n", "    cluster10.randomVisualizer().clip(geometry), \n", "    {}, \n", "    '10 clusters'\n", ")\n", "print(\"已添加10聚类结果到地图\")\n", "\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 分析结果\n", "\n", "聚类结果说明：\n", "- **3个聚类**：显示主要的土地覆盖类型\n", "- **5个聚类**：提供更细致的分类\n", "- **10个聚类**：显示详细的土地利用模式，包括不同作物类型\n", "\n", "嵌入向量的优势：\n", "- 包含空间上下文信息，同一对象内像素具有相似嵌入\n", "- 包含时间上下文信息，能检测具有相似时间模式的像素\n", "- 适合作物类型映射，因为能捕获作物物候和气候变量"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图包含以下图层：\n", "1. Embeddings Image - 嵌入数据的RGB可视化\n", "2. 3 clusters - 3聚类结果\n", "3. 5 clusters - 5聚类结果\n", "4. 10 clusters - 10聚类结果\n", "\n", "可以在地图界面中切换不同图层进行比较\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cff3140b4dbe4b59bcbdb92d0c9d9a55", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=488058.0, center=[12.442611268597101, 76.55565261840822], controls=(WidgetControl(options=['positio…"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示交互式地图\n", "print(\"地图包含以下图层：\")\n", "print(\"1. Embeddings Image - 嵌入数据的RGB可视化\")\n", "print(\"2. 3 clusters - 3聚类结果\")\n", "print(\"3. 5 clusters - 5聚类结果\") \n", "print(\"4. 10 clusters - 10聚类结果\")\n", "print(\"\\n可以在地图界面中切换不同图层进行比较\")\n", "\n", "# 显示地图\n", "Map"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}