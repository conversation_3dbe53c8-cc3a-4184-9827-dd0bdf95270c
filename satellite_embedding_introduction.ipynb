# 导入必要的库
import ee
import geemap

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")

# 创建交互式地图
Map = geemap.Map()

# 访问卫星嵌入数据集
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

# 设置地图为卫星底图
Map.add_basemap('SATELLITE')

print("地图已创建，卫星嵌入数据集已加载")

# 定义感兴趣的区域 - 印度克里希纳拉贾萨加拉(KRS)水库附近
geometry = ee.Geometry.Polygon([[
    [76.3978, 12.5521],
    [76.3978, 12.3550],
    [76.6519, 12.3550],
    [76.6519, 12.5521]
]])

# 将地图中心设置到感兴趣区域
Map.centerObject(geometry, 12)

print("感兴趣区域已定义（印度KRS水库附近）")

# 准备卫星嵌入数据集
# 设置年份和日期范围
year = 2024
start_date = ee.Date.fromYMD(year, 1, 1)
end_date = start_date.advance(1, 'year')

print(f"数据过滤设置：")
print(f"- 目标年份：{year}")
print(f"- 开始日期：{year}-01-01")
print(f"- 结束日期：{year+1}-01-01")

# 应用时间和空间过滤
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

print("- 已应用时间过滤器")
print("- 已应用空间过滤器")

# 将多个图块合并为一张图片
embeddings_image = filtered_embeddings.mosaic()

print("数据处理：")
print("- 已将过滤后的图像集合镶嵌为单一图像")
print("\n卫星嵌入图像信息：")
print("- 波段数量：64个（A00到A63）")
print("- 每个波段代表嵌入空间的一个维度")
print("- 所有64个波段共同构成完整的嵌入向量")

# 选择三个波段进行RGB可视化
vis_params = {
    'min': -0.3, 
    'max': 0.3, 
    'bands': ['A01', 'A16', 'A09']
}

# 添加嵌入图像到地图
Map.addLayer(
    embeddings_image.clip(geometry), 
    vis_params, 
    'Embeddings Image'
)

print("已添加嵌入图像可视化图层（使用波段A01, A16, A09作为RGB）")

Map

# 设置采样参数
n_samples = 1000
print(f"采样像素数量：{n_samples}")

# 从嵌入图像中采样像素用于训练聚类器
training = embeddings_image.sample(**{
    'region': geometry,
    'scale': 10,
    'numPixels': n_samples,
    'seed': 100
})

print("已完成像素采样，用于训练聚类模型")

# 定义聚类函数
def get_clusters(n_clusters):
    """
    训练K-means聚类器并对图像进行聚类
    
    参数:
        n_clusters: 聚类数量
    
    返回:
        聚类后的图像
    """
    # 创建并训练K-means聚类器
    clusterer = ee.Clusterer.wekaKMeans(n_clusters).train(training)
    
    # 对图像进行聚类
    clustered = embeddings_image.cluster(clusterer)
    return clustered

print("聚类函数已定义")

# 生成不同数量的聚类结果
print("--- 生成3个聚类 ---")
cluster3 = get_clusters(3)
Map.addLayer(
    cluster3.randomVisualizer().clip(geometry), 
    {}, 
    '3 clusters'
)
print("已添加3聚类结果到地图")

print("\n--- 生成5个聚类 ---")
cluster5 = get_clusters(5)
Map.addLayer(
    cluster5.randomVisualizer().clip(geometry), 
    {}, 
    '5 clusters'
)
print("已添加5聚类结果到地图")

print("\n--- 生成10个聚类 ---")
cluster10 = get_clusters(10)
Map.addLayer(
    cluster10.randomVisualizer().clip(geometry), 
    {}, 
    '10 clusters'
)
print("已添加10聚类结果到地图")

Map

# 显示交互式地图
print("地图包含以下图层：")
print("1. Embeddings Image - 嵌入数据的RGB可视化")
print("2. 3 clusters - 3聚类结果")
print("3. 5 clusters - 5聚类结果") 
print("4. 10 clusters - 10聚类结果")
print("\n可以在地图界面中切换不同图层进行比较")

# 显示地图
Map