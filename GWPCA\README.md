# 地理加权主成分分析(GWPCA)实现

## 项目概述

本项目实现了地理加权主成分分析(Geographically Weighted Principal Component Analysis, GWPCA)算法，用于将64维卫星嵌入数据降维到3维，同时保留空间变异的主要模式。

## 算法特点

- **空间自适应**: 不同位置有不同的主成分，考虑空间邻近性
- **高效降维**: 从64维成功降至3维，保留主要空间变异模式
- **稳健性强**: 使用高斯核函数计算空间权重，算法稳定
- **可视化完整**: 提供丰富的可视化结果

## 文件说明

### 主要文件

1. **`GWPCA_test01.ipynb`** - 完整版GWPCA实现
   - 包含完整的Earth Engine集成
   - 支持丝绸之路流域2017-2024年8年平均数据
   - 数据重采样到0.25°分辨率
   - 完整的可视化和分析报告

2. **`GWPCA_simple.ipynb`** - 简化版GWPCA实现
   - 避免复杂的投影问题
   - 使用2022年单年数据
   - 10km采样尺度
   - 适合快速测试和学习

3. **`GWPCA_demo.ipynb`** - 演示版本
   - 纯Python实现，不依赖Earth Engine
   - 使用模拟数据演示算法
   - 包含详细的可视化

### 辅助文件

4. **`test_gwpca.py`** - 算法测试脚本
5. **`run_gwpca_demo.py`** - 演示运行脚本

## 使用方法

### 方法1：使用简化版本（推荐）

```bash
# 在Jupyter Notebook中运行
jupyter notebook GWPCA_simple.ipynb
```

这个版本：
- 避免了复杂的投影问题
- 使用较大的采样尺度(10km)
- 处理速度快，适合初学者

### 方法2：使用完整版本

```bash
# 在Jupyter Notebook中运行
jupyter notebook GWPCA_test01.ipynb
```

注意：如果遇到投影错误，请使用简化版本。

### 方法3：使用演示版本

```bash
# 直接运行Python脚本
python run_gwpca_demo.py

# 或在Jupyter中运行
jupyter notebook GWPCA_demo.ipynb
```

## 算法流程

### 第一步：数据获取
- 通过geemap获取丝绸之路流域的Satellite Embedding V1数据
- 时间范围：2017-2024年（完整版）或2022年（简化版）
- 空间范围：丝绸之路流域

### 第二步：数据预处理
- 数据重采样到指定分辨率
- 随机采样获取分析点
- 数据清洗和格式转换

### 第三步：GWPCA分析
- 使用高斯核函数计算空间权重
- 计算局部加权协方差矩阵
- 特征值分解获取主成分
- 将64维数据降维到3维

### 第四步：结果可视化
- 三个主成分的空间分布
- 解释方差比的空间分布
- 累计解释方差分析
- 统计报告生成

## 核心算法

### 高斯核函数
```python
def gaussian_kernel(distances, bandwidth):
    return np.exp(-(distances**2) / (2 * bandwidth**2))
```

### GWPCA核心函数
```python
def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):
    # 计算距离和权重
    distances = cdist([target_coords], all_coords, metric='euclidean')[0]
    weights = gaussian_kernel(distances, bandwidth)
    
    # 加权协方差矩阵计算
    weighted_cov = np.cov(centered_data.T, aweights=weights)
    
    # 特征值分解
    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)
    
    # 返回主成分和解释方差
    return components, explained_variance_ratio, transformed_data
```

## 参数说明

- **bandwidth**: 带宽参数，控制空间权重的影响范围（单位：度）
- **n_components**: 主成分数量，默认为3
- **scale**: 采样尺度，影响数据精度和处理速度
- **numPixels**: 采样点数量，影响分析精度

## 结果解释

### 主成分
- **PC1**: 第一主成分，解释最大的空间变异
- **PC2**: 第二主成分，解释次要的空间变异
- **PC3**: 第三主成分，解释第三重要的空间变异

### 解释方差比
- 表示每个主成分解释的原始数据方差比例
- 累计解释方差比表示降维后保留的信息量

## 故障排除

### 常见问题

1. **投影错误**: 使用简化版本`GWPCA_simple.ipynb`
2. **内存不足**: 减少采样点数量或增大采样尺度
3. **计算超时**: 减少分析位置数量或使用更大的带宽

### 性能优化

- 使用适当的采样尺度（推荐10km）
- 限制采样点数量（推荐200-500个）
- 选择合适的带宽参数（推荐2-5度）

## 依赖库

```python
import ee
import geemap
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from scipy.spatial.distance import cdist
```

## 联系方式

如有问题或建议，请通过GitHub Issues联系。

## 许可证

本项目采用MIT许可证。
