#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GWPCA测试脚本
测试地理加权主成分分析算法的核心功能
"""

import numpy as np
from scipy.spatial.distance import cdist

def gaussian_kernel(distances, bandwidth):
    """
    高斯核函数计算空间权重
    """
    return np.exp(-(distances**2) / (2 * bandwidth**2))

def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):
    """
    在指定位置执行地理加权主成分分析
    
    参数:
        target_coords: 目标位置坐标 [lon, lat]
        all_coords: 所有样本坐标 (n_samples, 2)
        all_data: 所有样本数据 (n_samples, n_features)
        bandwidth: 带宽参数
        n_components: 主成分数量
    
    返回:
        components: 主成分
        explained_variance_ratio: 解释方差比
        transformed_data: 降维后的数据
    """
    # 计算距离
    distances = cdist([target_coords], all_coords, metric='euclidean')[0]
    
    # 计算权重
    weights = gaussian_kernel(distances, bandwidth)
    
    # 权重归一化
    weights = weights / np.sum(weights)
    
    # 加权数据标准化
    weighted_mean = np.average(all_data, weights=weights, axis=0)
    centered_data = all_data - weighted_mean
    
    # 计算加权协方差矩阵
    weighted_cov = np.cov(centered_data.T, aweights=weights)
    
    # 特征值分解
    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)
    
    # 按特征值降序排列
    idx = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # 选择前n_components个主成分
    components = eigenvectors[:, :n_components]
    explained_variance = eigenvalues[:n_components]
    explained_variance_ratio = explained_variance / np.sum(eigenvalues)
    
    # 变换目标位置的数据
    target_idx = np.argmin(distances)
    target_data = all_data[target_idx] - weighted_mean
    transformed_data = np.dot(target_data, components)
    
    return components, explained_variance_ratio, transformed_data

def test_gwpca():
    """
    测试GWPCA算法
    """
    print("=== GWPCA算法测试 ===")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 100
    n_features = 64  # 模拟64维卫星嵌入数据
    
    # 生成随机坐标（模拟地理坐标）
    coords = np.random.uniform(low=[60, 25], high=[120, 50], size=(n_samples, 2))
    
    # 生成随机数据（模拟卫星嵌入数据）
    data = np.random.randn(n_samples, n_features)
    
    print(f"测试数据生成完成:")
    print(f"- 样本数量: {n_samples}")
    print(f"- 特征维度: {n_features}")
    print(f"- 坐标范围: 经度 {coords[:, 0].min():.2f}-{coords[:, 0].max():.2f}, 纬度 {coords[:, 1].min():.2f}-{coords[:, 1].max():.2f}")
    
    # 测试GWPCA
    target_coord = [90, 37.5]  # 测试点
    bandwidth = 5.0  # 带宽
    
    print(f"\n执行GWPCA分析:")
    print(f"- 目标坐标: {target_coord}")
    print(f"- 带宽: {bandwidth}")
    
    try:
        components, var_ratios, transformed = gwpca_at_location(
            target_coord, coords, data, bandwidth, n_components=3
        )
        
        print(f"\nGWPCA分析成功!")
        print(f"- 主成分形状: {components.shape}")
        print(f"- 解释方差比: {var_ratios}")
        print(f"- 降维后数据: {transformed}")
        print(f"- 累计解释方差比: {var_ratios.sum():.4f}")
        
        return True
        
    except Exception as e:
        print(f"\nGWPCA分析失败: {e}")
        return False

if __name__ == "__main__":
    success = test_gwpca()
    if success:
        print("\n✓ GWPCA算法测试通过!")
    else:
        print("\n✗ GWPCA算法测试失败!")
