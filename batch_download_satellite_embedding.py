#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量下载Satellite Embedding数据脚本
按年份和波段组织下载，支持断点续传
"""

import ee
import geemap
import os
import time
import json
from pathlib import Path
from datetime import datetime

def initialize_ee():
    """初始化Earth Engine"""
    try:
        geemap.set_proxy(port=33210)
        ee.Initialize()
        print("✓ Earth Engine初始化成功")
        return True
    except Exception as e:
        print(f"✗ Earth Engine初始化失败: {e}")
        return False

def create_directories(base_path, years):
    """创建年份目录"""
    year_paths = {}
    Path(base_path).mkdir(parents=True, exist_ok=True)
    
    for year in years:
        year_path = os.path.join(base_path, str(year))
        Path(year_path).mkdir(parents=True, exist_ok=True)
        year_paths[year] = year_path
    
    print(f"✓ 已创建 {len(year_paths)} 个年份目录")
    return year_paths

def download_band(year, band, roi, output_path, crs, scale, max_retries=3):
    """
    下载指定年份和波段的数据，支持重试
    """
    for attempt in range(max_retries):
        try:
            # 检查文件是否已存在且有效
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                if file_size > 1000:  # 文件大小大于1KB认为有效
                    return True, "文件已存在"
            
            # 加载数据
            embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
            
            start_date = ee.Date.fromYMD(year, 1, 1)
            end_date = start_date.advance(1, 'year')
            
            filtered = embeddings \
                .filter(ee.Filter.date(start_date, end_date)) \
                .filter(ee.Filter.bounds(roi))
            
            image = filtered.mosaic().select([band])
            
            # 下载
            geemap.download_ee_image(
                image=image,
                filename=output_path,
                region=roi,
                crs=crs,
                scale=scale,
            )
            
            # 验证下载
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                if file_size > 1000:
                    return True, f"下载成功 ({file_size/1024:.1f}KB)"
                else:
                    os.remove(output_path)  # 删除无效文件
                    raise Exception("下载文件过小")
            else:
                raise Exception("文件未创建")
                
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"    重试 {attempt + 1}/{max_retries}: {e}")
                time.sleep(5)  # 等待5秒后重试
            else:
                return False, str(e)
    
    return False, "重试次数用尽"

def save_progress(progress_file, completed_files):
    """保存下载进度"""
    with open(progress_file, 'w') as f:
        json.dump(completed_files, f, indent=2)

def load_progress(progress_file):
    """加载下载进度"""
    if os.path.exists(progress_file):
        with open(progress_file, 'r') as f:
            return json.load(f)
    return []

def main():
    """主函数"""
    print("=" * 60)
    print("Satellite Embedding 0.25° 数据批量下载工具")
    print("=" * 60)
    
    # 初始化Earth Engine
    if not initialize_ee():
        return
    
    # 配置参数
    config = {
        'base_path': r"H:\satellite_embedding\basins_big_025",
        'start_year': 2017,  # 建议先从单年开始测试
        'end_year': 2024,    # 建议先从单年开始测试
        'roi': ee.Geometry.Polygon([
            [-10, 10], [-10, 60], [150, 60], [150, 10]  # 全球范围
        ]),
        'crs': "EPSG:4326",
        'scale': 27830,
        # 'bands': ['A00', 'A01', 'A02']  # 建议先测试前3个波段
        'bands': [f'A{i:02d}' for i in range(64)]  # 完整波段列表
    }
    
    years = list(range(config['start_year'], config['end_year'] + 1))
    
    print(f"配置信息:")
    print(f"- 保存路径: {config['base_path']}")
    print(f"- 年份范围: {config['start_year']}-{config['end_year']} ({len(years)}年)")
    print(f"- 波段数量: {len(config['bands'])}个")
    print(f"- 总文件数: {len(years) * len(config['bands'])}个")
    print(f"- 研究区域: 全球 (-180°-180°E, -90°-90°N)")
    print(f"- 分辨率: 0.25°")
    print(f"⚠️  警告: 全球数据文件将非常大！")
    print(f"⚠️  建议先运行测试版本，确认无误后再扩展")
    
    # 创建目录
    year_paths = create_directories(config['base_path'], years)
    
    # 进度文件
    progress_file = os.path.join(config['base_path'], 'download_progress.json')
    completed_files = load_progress(progress_file)
    
    print(f"✓ 已完成文件数: {len(completed_files)}")
    
    # 开始下载
    total_files = len(years) * len(config['bands'])
    current_count = len(completed_files)
    failed_files = []
    
    start_time = time.time()
    
    print(f"\n开始下载...")
    
    for year in years:
        print(f"\n--- 处理 {year} 年 ---")
        year_path = year_paths[year]
        
        for band in config['bands']:
            filename = f"{band}_{year}.tif"
            file_key = f"{year}_{band}"
            
            # 跳过已完成的文件
            if file_key in completed_files:
                continue
            
            output_path = os.path.join(year_path, filename)
            
            print(f"  下载 {band}...", end=" ")
            
            success, message = download_band(
                year, band, config['roi'], output_path, 
                config['crs'], config['scale']
            )
            
            if success:
                completed_files.append(file_key)
                current_count += 1
                print(f"✓ {message}")
                
                # 每10个文件保存一次进度
                if current_count % 10 == 0:
                    save_progress(progress_file, completed_files)
                    elapsed = time.time() - start_time
                    progress = current_count / total_files * 100
                    print(f"    进度: {current_count}/{total_files} ({progress:.1f}%) - 用时 {elapsed/60:.1f}分钟")
            else:
                failed_files.append((year, band, message))
                print(f"✗ {message}")
            
            # 短暂延迟
            time.sleep(1)
    
    # 保存最终进度
    save_progress(progress_file, completed_files)
    
    # 生成报告
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n" + "=" * 60)
    print(f"下载完成!")
    print(f"总用时: {total_time/60:.1f} 分钟")
    print(f"成功下载: {current_count}/{total_files} 个文件")
    print(f"失败: {len(failed_files)} 个文件")
    print(f"成功率: {current_count/total_files*100:.1f}%")
    
    if failed_files:
        print(f"\n失败文件:")
        for year, band, error in failed_files[:10]:
            print(f"  {year}/{band}: {error}")
        if len(failed_files) > 10:
            print(f"  ... 还有 {len(failed_files)-10} 个失败文件")
    
    # 保存详细报告
    report_path = os.path.join(config['base_path'], 'download_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"Satellite Embedding 下载报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总用时: {total_time/60:.1f} 分钟\n")
        f.write(f"成功: {current_count}/{total_files}\n")
        f.write(f"失败: {len(failed_files)}\n")
        f.write(f"成功率: {current_count/total_files*100:.1f}%\n\n")
        
        if failed_files:
            f.write("失败文件:\n")
            for year, band, error in failed_files:
                f.write(f"{year}/{band}: {error}\n")
    
    print(f"\n详细报告已保存: {report_path}")

if __name__ == "__main__":
    main()
