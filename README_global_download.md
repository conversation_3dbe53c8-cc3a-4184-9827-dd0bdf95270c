# 全球 Satellite Embedding 数据下载工具

## 概述

本工具用于批量下载Google Earth Engine上的全球Satellite Embedding V1数据，按年份和波段组织，重采样到0.25°分辨率。

## ⚠️ 重要警告

### 数据规模警告
- **文件巨大**: 全球数据每个文件可能达到几百MB到几GB
- **总数据量**: 完整数据集可能达到TB级别
- **下载时间**: 每个文件可能需要数小时，完整数据集需要数天到数周
- **存储需求**: 建议预留至少500GB-1TB空间
- **网络要求**: 需要稳定的高速网络连接

### 配额限制
- Earth Engine有下载配额限制
- 可能遇到请求频率限制
- 建议分批次下载

## 文件说明

### 主要文件

1. **`download_satellite_embedding_global.ipynb`** - 全球数据下载notebook
   - 默认配置为测试模式（2022年，前3个波段）
   - 包含详细的警告和说明
   - 支持逐步扩展下载范围

2. **`batch_download_satellite_embedding.py`** - 批处理脚本（已更新为全球版本）
   - 命令行批量下载
   - 支持断点续传
   - 自动重试机制

3. **原有的区域版本文件仍然保留**
   - `download_satellite_embedding_025.ipynb` - 丝绸之路流域版本
   - `test_download_satellite_embedding.ipynb` - 区域测试版本

## 数据组织结构

```
H:\satellite_embedding\global_025\
├── 2017\
│   ├── A00_2017_global.tif  (几百MB-几GB)
│   ├── A01_2017_global.tif
│   ├── ...
│   └── A63_2017_global.tif
├── 2018\
│   └── ...
└── 2024\
    └── ...
```

## 使用方法

### 强烈推荐的渐进式下载策略

#### 第1步：单波段测试
```python
# 在notebook中设置
start_year = 2022
end_year = 2022
bands = ['A00']  # 只下载一个波段
```

#### 第2步：多波段测试
```python
# 确认单波段正常后
bands = ['A00', 'A01', 'A02']  # 下载前3个波段
```

#### 第3步：单年完整下载
```python
# 确认多波段正常后
bands = [f'A{i:02d}' for i in range(64)]  # 全部64个波段
```

#### 第4步：多年下载
```python
# 确认单年正常后
start_year = 2017
end_year = 2024
```

### 具体操作步骤

1. **运行测试版本**：
   ```bash
   jupyter notebook download_satellite_embedding_global.ipynb
   ```

2. **检查测试结果**：
   - 验证文件大小合理（几百MB-几GB）
   - 确认下载时间可接受
   - 检查磁盘空间充足

3. **逐步扩展**：
   - 根据测试结果调整参数
   - 逐步增加波段和年份

## 配置参数

### 关键参数说明

```python
# 基本配置
base_path = r"H:\satellite_embedding\global_025"  # 保存路径
start_year = 2022  # 开始年份
end_year = 2022    # 结束年份

# 波段配置
bands = ['A00', 'A01', 'A02']  # 测试用
# bands = [f'A{i:02d}' for i in range(64)]  # 完整版

# 全球区域
roi = ee.Geometry.Polygon([
    [-180, -90], [-180, 90], [180, 90], [180, -90]
])

# 分辨率
scale = 27830  # 约0.25度
```

## 预期数据量估算

### 单个文件大小
- **0.25°分辨率全球数据**: 约1440×720像素
- **预计文件大小**: 50MB-2GB（取决于数据压缩和内容）
- **64个波段**: 3.2GB-128GB每年

### 完整数据集（2017-2024，64波段）
- **总文件数**: 8年 × 64波段 = 512个文件
- **预计总大小**: 25GB-1TB
- **下载时间**: 数天到数周

## 性能优化建议

### 网络优化
1. **使用有线网络**，避免WiFi不稳定
2. **确保网络带宽充足**（建议100Mbps以上）
3. **避免网络高峰期**下载

### 存储优化
1. **使用SSD硬盘**提高写入速度
2. **确保充足空间**（建议预留2倍数据量的空间）
3. **定期清理临时文件**

### 下载策略
1. **分时段下载**，避免长时间连续下载
2. **监控系统资源**，避免过载
3. **备份重要数据**，防止意外丢失

## 故障排除

### 常见问题

1. **下载超时**
   - 减少并发下载数量
   - 增加重试次数
   - 分批次下载

2. **配额限制**
   - 等待配额重置
   - 分时段下载
   - 联系Earth Engine支持

3. **磁盘空间不足**
   - 清理不必要文件
   - 使用外部存储
   - 压缩已下载数据

4. **网络中断**
   - 使用断点续传功能
   - 检查网络稳定性
   - 重新运行下载脚本

### 错误处理

程序包含以下错误处理机制：
- 自动重试（最多3次）
- 断点续传支持
- 详细错误日志
- 进度保存和恢复

## 数据验证

### 自动验证
- 文件存在性检查
- 文件大小验证（>1MB）
- 完整性统计

### 手动验证
```python
import rasterio
with rasterio.open('path/to/file.tif') as src:
    print(f"形状: {src.shape}")
    print(f"投影: {src.crs}")
    print(f"范围: {src.bounds}")
    print(f"数据类型: {src.dtypes}")
```

## 使用建议

### 首次使用者
1. **从测试版本开始**
2. **仔细阅读所有警告**
3. **确保充足的存储空间**
4. **在稳定网络环境下操作**

### 高级用户
1. **可以并行下载不同年份**
2. **考虑使用云存储**
3. **实施数据压缩策略**
4. **建立数据备份计划**

## 法律和伦理考虑

1. **遵守Earth Engine使用条款**
2. **合理使用下载配额**
3. **数据仅供研究使用**
4. **尊重数据提供者的权利**

## 技术支持

如遇问题请检查：
1. Earth Engine认证状态
2. 网络连接和代理设置
3. 磁盘空间和权限
4. 错误日志和报告文件

## 更新日志

- v2.0: 支持全球数据下载
- v2.1: 添加渐进式下载策略
- v2.2: 优化错误处理和进度显示
