"""
测试Jupyter notebook文件的语法正确性
"""

import json
import sys

def test_notebook_syntax(notebook_path):
    """测试notebook文件的JSON语法和基本结构"""
    try:
        # 测试JSON语法
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        
        print(f"✓ {notebook_path} JSON语法正确")
        
        # 检查基本结构
        required_keys = ['cells', 'metadata', 'nbformat', 'nbformat_minor']
        for key in required_keys:
            if key not in notebook:
                print(f"✗ 缺少必需的键: {key}")
                return False
        
        print(f"✓ notebook结构完整")
        
        # 检查cells
        cells = notebook['cells']
        print(f"✓ 包含 {len(cells)} 个单元格")
        
        # 统计不同类型的单元格
        cell_types = {}
        for cell in cells:
            cell_type = cell.get('cell_type', 'unknown')
            cell_types[cell_type] = cell_types.get(cell_type, 0) + 1
        
        print(f"✓ 单元格类型统计: {cell_types}")
        
        # 检查代码单元格的基本语法
        code_cells = [cell for cell in cells if cell.get('cell_type') == 'code']
        print(f"✓ 检查 {len(code_cells)} 个代码单元格")
        
        for i, cell in enumerate(code_cells):
            source = cell.get('source', [])
            if isinstance(source, list):
                code = ''.join(source)
            else:
                code = source
            
            # 基本语法检查（编译但不执行）
            try:
                compile(code, f'<cell_{i}>', 'exec')
            except SyntaxError as e:
                print(f"✗ 代码单元格 {i} 语法错误: {e}")
                print(f"   代码片段: {code[:100]}...")
                return False
        
        print(f"✓ 所有代码单元格语法正确")
        return True
        
    except json.JSONDecodeError as e:
        print(f"✗ JSON语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Jupyter Notebook 语法测试 ===\n")
    
    notebooks = [
        "satellite_embedding_introduction.ipynb",
        "satellite_embedding_unsupervised_classification.ipynb",
        "satellite_embedding_supervised_classification.ipynb",
        "satellite_embedding_regression.ipynb",
        "satellite_embedding_similarity_search.ipynb"
    ]
    
    all_passed = True
    
    for notebook in notebooks:
        print(f"测试 {notebook}:")
        try:
            if test_notebook_syntax(notebook):
                print(f"🎉 {notebook} 测试通过\n")
            else:
                print(f"❌ {notebook} 测试失败\n")
                all_passed = False
        except FileNotFoundError:
            print(f"⚠️  文件不存在: {notebook}\n")
            all_passed = False
        except Exception as e:
            print(f"❌ 测试异常: {e}\n")
            all_passed = False
    
    if all_passed:
        print("🎉 所有notebook文件测试通过！")
    else:
        print("❌ 部分notebook文件存在问题")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
