#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行GWPCA演示
展示地理加权主成分分析的核心功能
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

def gaussian_kernel(distances, bandwidth):
    """高斯核函数计算空间权重"""
    return np.exp(-(distances**2) / (2 * bandwidth**2))

def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):
    """在指定位置执行地理加权主成分分析"""
    # 计算距离
    distances = cdist([target_coords], all_coords, metric='euclidean')[0]
    
    # 计算权重
    weights = gaussian_kernel(distances, bandwidth)
    weights = weights / np.sum(weights)
    
    # 加权数据标准化
    weighted_mean = np.average(all_data, weights=weights, axis=0)
    centered_data = all_data - weighted_mean
    
    # 计算加权协方差矩阵
    weighted_cov = np.cov(centered_data.T, aweights=weights)
    
    # 特征值分解
    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)
    
    # 按特征值降序排列
    idx = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # 选择前n_components个主成分
    components = eigenvectors[:, :n_components]
    explained_variance = eigenvalues[:n_components]
    explained_variance_ratio = explained_variance / np.sum(eigenvalues)
    
    # 变换目标位置的数据
    target_idx = np.argmin(distances)
    target_data = all_data[target_idx] - weighted_mean
    transformed_data = np.dot(target_data, components)
    
    return components, explained_variance_ratio, transformed_data

def main():
    print("=== 地理加权主成分分析(GWPCA)演示 ===")
    
    # 生成模拟的丝绸之路流域数据
    print("\n1. 生成模拟数据")
    np.random.seed(42)
    
    # 模拟丝绸之路流域的地理范围
    lon_range = [60, 120]  # 经度范围
    lat_range = [25, 50]   # 纬度范围
    
    # 生成0.5度分辨率的网格点（为了演示，使用较粗分辨率）
    lons = np.arange(lon_range[0], lon_range[1], 0.5)
    lats = np.arange(lat_range[0], lat_range[1], 0.5)
    
    # 创建网格
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    coordinates = np.column_stack([lon_grid.ravel(), lat_grid.ravel()])
    
    # 模拟64维卫星嵌入数据
    n_samples = len(coordinates)
    n_features = 64
    
    # 生成具有空间相关性的数据
    embeddings_data = np.random.randn(n_samples, n_features)
    
    # 添加空间趋势
    for i in range(n_features):
        # 添加经度趋势
        embeddings_data[:, i] += 0.1 * coordinates[:, 0] / 100
        # 添加纬度趋势
        embeddings_data[:, i] += 0.1 * coordinates[:, 1] / 50
    
    print(f"   - 样本数量: {n_samples}")
    print(f"   - 特征维度: {n_features}")
    print(f"   - 经度范围: {coordinates[:, 0].min():.1f} - {coordinates[:, 0].max():.1f}")
    print(f"   - 纬度范围: {coordinates[:, 1].min():.1f} - {coordinates[:, 1].max():.1f}")
    
    # 执行GWPCA分析
    print("\n2. 执行GWPCA分析")
    
    # 设置参数
    bandwidth = 2.0  # 带宽参数（度）
    n_components = 3  # 主成分数量
    
    print(f"   - 带宽: {bandwidth}度")
    print(f"   - 主成分数量: {n_components}")
    
    # 选择一些代表性位置进行分析
    test_locations = [
        [70, 30],   # 西南
        [90, 40],   # 中心
        [110, 45],  # 东北
        [80, 35],   # 中西
        [100, 30]   # 中东
    ]
    
    results = []
    
    print(f"\n   开始分析 {len(test_locations)} 个测试位置...")
    
    for i, location in enumerate(test_locations):
        print(f"   分析位置 {i+1}: [{location[0]}, {location[1]}]")
        
        try:
            components, var_ratios, transformed = gwpca_at_location(
                location, coordinates, embeddings_data, bandwidth, n_components
            )
            
            results.append({
                'location': location,
                'components': components,
                'var_ratios': var_ratios,
                'transformed': transformed
            })
            
            print(f"     - 解释方差比: [{var_ratios[0]:.3f}, {var_ratios[1]:.3f}, {var_ratios[2]:.3f}]")
            print(f"     - 累计解释方差: {var_ratios.sum():.4f}")
            
        except Exception as e:
            print(f"     - 分析失败: {e}")
    
    print(f"\n   GWPCA分析完成，成功分析 {len(results)} 个位置")
    
    # 生成分析报告
    print("\n3. 分析报告")
    print(f"   数据概况:")
    print(f"   - 研究区域：丝绸之路流域 (60°-120°E, 25°-50°N)")
    print(f"   - 总样本数：{n_samples}")
    print(f"   - 原始维度：{n_features}维")
    print(f"   - 降维后维度：{n_components}维")
    
    print(f"\n   主成分统计:")
    for i in range(n_components):
        pc_values = [r['transformed'][i] for r in results]
        var_ratios = [r['var_ratios'][i] for r in results]
        print(f"   第{i+1}主成分 (PC{i+1}):")
        print(f"     - 数值范围：{min(pc_values):.4f} - {max(pc_values):.4f}")
        print(f"     - 平均解释方差比：{np.mean(var_ratios):.4f}")
    
    avg_cumulative = np.mean([sum(r['var_ratios']) for r in results])
    print(f"\n   总体性能:")
    print(f"   - 平均累计解释方差比：{avg_cumulative:.4f}")
    print(f"   - 信息保留率：{avg_cumulative*100:.2f}%")
    
    print(f"\n=== 演示完成 ===")
    print(f"算法特点:")
    print(f"- ✓ 考虑空间邻近性的局部主成分分析")
    print(f"- ✓ 保留了主要的空间变异模式")
    print(f"- ✓ 有效降维：从{n_features}维降至{n_components}维")
    print(f"- ✓ 空间自适应：不同位置有不同的主成分")

if __name__ == "__main__":
    main()
