# 导入必要的库
import ee
import geemap
import os
from pathlib import Path
import time

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")
print("开始准备下载全球Satellite Embedding数据")
print("⚠️  警告: 这将下载非常大的数据文件！")

# 设置参数
print("=== 设置全球下载参数 ===")

# 目标路径
base_path = r"H:\satellite_embedding\basins_big_025_2"

# 年份范围（建议先下载单年测试）
start_year = 2017  # 建议先从单年开始
end_year = 2024    # 建议先从单年开始
years = list(range(start_year, end_year + 1))

# 波段列表（建议先下载部分波段测试）
# 完整波段: [f'A{i:02d}' for i in range(64)]
# bands = ['A00', 'A01', 'A02']  # 先测试前3个波段
bands = [f'A{i:02d}' for i in range(64)]

# 研究区域：全球
roi = ee.Geometry.Polygon([[
    [-10, 10],   # 西南角
    [-10, 60],    # 西北角
    [150, 60],     # 东北角
    [150, 10]     # 东南角
]])

# 下载参数
crs = "EPSG:4326"
scale = 27830  # 约0.25度分辨率

print(f"目标路径: {base_path}")
print(f"年份范围: {start_year}-{end_year} ({len(years)}年)")
print(f"波段数量: {len(bands)}个 (测试用，完整版本为64个)")
print(f"研究区域: 全球 (-180°-180°E, -90°-90°N)")
print(f"分辨率: 0.25° (scale={scale})")
print(f"投影: {crs}")
print(f"")
print(f"⚠️  预计每个文件大小: 几百MB到几GB")
print(f"⚠️  总数据量: 可能达到TB级别")
print(f"⚠️  建议先运行此测试版本，确认无误后再扩展到全部波段和年份")

# 创建基础目录
Path(base_path).mkdir(parents=True, exist_ok=True)
print(f"已创建基础目录: {base_path}")

# 创建年份目录
print("\n=== 创建年份目录 ===")

year_paths = {}
for year in years:
    year_path = os.path.join(base_path, str(year))
    Path(year_path).mkdir(parents=True, exist_ok=True)
    year_paths[year] = year_path
    print(f"已创建目录: {year_path}")

print(f"\n共创建 {len(year_paths)} 个年份目录")

# 定义全球下载函数
def download_global_band(year, band, roi, output_path, crs, scale):
    """
    下载指定年份和波段的全球satellite embedding数据
    
    参数:
        year: 年份
        band: 波段名称 (如 'A00')
        roi: 研究区域（全球）
        output_path: 输出文件路径
        crs: 坐标系
        scale: 分辨率
    """
    try:
        print(f"    正在准备 {year}年 {band}波段的全球数据...")
        
        # 加载satellite embedding数据集
        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
        
        # 设置时间范围
        start_date = ee.Date.fromYMD(year, 1, 1)
        end_date = start_date.advance(1, 'year')
        
        # 过滤数据
        filtered = embeddings \
            .filter(ee.Filter.date(start_date, end_date))
        
        # 创建镶嵌图并选择指定波段
        image = filtered.mosaic().select([band])
        
        print(f"    数据准备完成，开始下载全球数据...")
        print(f"    ⚠️  警告: 这可能需要很长时间，文件可能非常大！")
        
        # 下载图像
        geemap.download_ee_image(
            image=image,
            filename=output_path,
            region=roi,
            crs=crs,
            scale=scale,
        )
        
        # 检查文件是否存在
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"    ✓ 下载成功: {output_path}")
            print(f"    文件大小: {file_size/1024/1024:.1f} MB")
            return True, None
        else:
            return False, "文件未创建"
        
    except Exception as e:
        return False, str(e)

print("全球下载函数已定义")
print("⚠️  注意: 全球数据下载可能遇到以下问题:")
print("   - 文件过大导致下载超时")
print("   - Earth Engine配额限制")
print("   - 网络连接中断")
print("   - 磁盘空间不足")

# 执行全球数据下载
print("\n=== 开始全球数据下载 ===")

total_files = len(years) * len(bands)
completed = 0
failed = 0
failed_list = []

print(f"总计需要下载: {total_files} 个全球文件")
print(f"⚠️  每个文件可能需要数小时下载")
print(f"⚠️  建议在稳定网络环境下运行")
print("\n开始下载...")

start_time = time.time()

for year in years:
    print(f"\n--- 处理 {year} 年 ---")
    year_path = year_paths[year]
    
    for i, band in enumerate(bands):
        # 构建输出文件路径
        filename = f"{band}_{year}_global.tif"
        output_path = os.path.join(year_path, filename)
        
        # 检查文件是否已存在
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            if file_size > 1000000:  # 文件大小大于1MB认为有效
                print(f"  跳过 {band} (文件已存在，大小: {file_size/1024/1024:.1f}MB)")
                completed += 1
                continue
        
        print(f"  下载全球 {band} -> {filename}")
        
        # 执行下载
        success, error = download_global_band(
            year, band, roi, output_path, crs, scale
        )
        
        if success:
            completed += 1
            print(f"    ✓ 成功 ({completed}/{total_files})")
        else:
            failed += 1
            failed_list.append((year, band, error))
            print(f"    ✗ 失败: {error}")
        
        # 显示进度
        elapsed = time.time() - start_time
        progress = completed / total_files * 100
        print(f"  进度: {progress:.1f}% (已用时 {elapsed/3600:.1f} 小时)")
        
        # 较长延迟避免请求过快
        time.sleep(5)

# 下载完成统计
end_time = time.time()
total_time = end_time - start_time

print(f"\n=== 全球数据下载完成 ===")
print(f"总用时: {total_time/3600:.1f} 小时")
print(f"成功下载: {completed} 个文件")
print(f"下载失败: {failed} 个文件")
print(f"成功率: {completed/total_files*100:.1f}%")

# 显示失败的下载
if failed_list:
    print(f"\n=== 失败的下载 ===")
    for year, band, error in failed_list:
        print(f"  {year}/{band}: {error}")
    
    print(f"\n可以重新运行此notebook来重试失败的下载")
else:
    print(f"\n🎉 所有全球文件下载成功！")

print(f"\n数据保存位置: {base_path}")

# 验证全球数据下载结果
print("\n=== 验证全球数据下载结果 ===")

total_expected = len(years) * len(bands)
total_found = 0
total_size = 0
missing_files = []

for year in years:
    year_path = year_paths[year]
    year_files = 0
    year_size = 0
    
    for band in bands:
        filename = f"{band}_{year}_global.tif"
        file_path = os.path.join(year_path, filename)
        
        if os.path.exists(file_path):
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > 1000000:  # 文件大小大于1MB认为有效
                year_files += 1
                total_found += 1
                year_size += file_size
                total_size += file_size
            else:
                missing_files.append((year, band, f"文件过小 ({file_size/1024:.1f}KB)"))
        else:
            missing_files.append((year, band, "文件不存在"))
    
    print(f"{year}年: {year_files}/{len(bands)} 个文件, 总大小: {year_size/1024/1024/1024:.2f} GB")

print(f"\n总计: {total_found}/{total_expected} 个文件")
print(f"总大小: {total_size/1024/1024/1024:.2f} GB")
print(f"完整性: {total_found/total_expected*100:.1f}%")

if missing_files:
    print(f"\n缺失文件 ({len(missing_files)}个):")
    for year, band, reason in missing_files:
        print(f"  {year}/{band}: {reason}")
else:
    print("\n✅ 所有全球文件下载完整！")

# 生成全球数据下载报告
print("\n=== 生成全球数据下载报告 ===")

report_path = os.path.join(base_path, "global_download_report.txt")

with open(report_path, 'w', encoding='utf-8') as f:
    f.write("全球 Satellite Embedding 0.25° 数据下载报告\n")
    f.write("=" * 60 + "\n\n")
    
    f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"总用时: {total_time/3600:.1f} 小时\n")
    f.write(f"研究区域: 全球 (-180°-180°E, -90°-90°N)\n")
    f.write(f"年份范围: {start_year}-{end_year}\n")
    f.write(f"波段范围: {bands}\n")
    f.write(f"分辨率: 0.25° (scale={scale})\n")
    f.write(f"投影: {crs}\n\n")
    
    f.write(f"下载统计:\n")
    f.write(f"- 预期文件数: {total_expected}\n")
    f.write(f"- 成功下载: {completed}\n")
    f.write(f"- 下载失败: {failed}\n")
    f.write(f"- 实际文件数: {total_found}\n")
    f.write(f"- 总数据大小: {total_size/1024/1024/1024:.2f} GB\n")
    f.write(f"- 完整性: {total_found/total_expected*100:.1f}%\n\n")
    
    if failed_list:
        f.write(f"失败列表:\n")
        for year, band, error in failed_list:
            f.write(f"- {year}/{band}: {error}\n")
        f.write("\n")
    
    if missing_files:
        f.write(f"缺失文件:\n")
        for year, band, reason in missing_files:
            f.write(f"- {year}/{band}: {reason}\n")
    
    f.write("\n数据组织结构:\n")
    for year in years:
        year_path = year_paths[year]
        year_files = len([f for f in os.listdir(year_path) if f.endswith('.tif')])
        year_size = sum(os.path.getsize(os.path.join(year_path, f)) 
                       for f in os.listdir(year_path) if f.endswith('.tif'))
        f.write(f"- {year}/: {year_files} 个TIF文件, {year_size/1024/1024/1024:.2f} GB\n")
    
    f.write("\n使用建议:\n")
    f.write("- 如需下载更多年份，请修改年份范围\n")
    f.write("- 如需下载更多波段，请修改波段列表\n")
    f.write("- 建议逐步扩展，避免一次性下载过多数据\n")
    f.write("- 注意监控磁盘空间和网络稳定性\n")
    f.write("- 如遇配额限制，可分时段下载\n")

print(f"全球数据下载报告已保存: {report_path}")
print(f"\n=== 全球数据下载任务完成 ===")
print(f"\n📊 总结:")
print(f"- 下载了 {total_found} 个全球数据文件")
print(f"- 总数据量: {total_size/1024/1024/1024:.2f} GB")
print(f"- 用时: {total_time/3600:.1f} 小时")
print(f"- 平均每文件: {total_size/total_found/1024/1024:.1f} MB" if total_found > 0 else "")
print(f"\n💡 下一步建议:")
print(f"- 检查下载的数据质量")
print(f"- 如需更多数据，可修改年份和波段范围")
print(f"- 考虑数据压缩以节省存储空间")
print(f"- 备份重要数据")