{"cells": [{"cell_type": "markdown", "id": "1e190423-9ee4-4747-b806-1ef3ee1bd1b2", "metadata": {}, "source": ["本文使用 GEE 并基于 MODIS 的多种火灾数据产品计算出了2022年湖北省各地市范围内的年度烧毁面积。\n", "\n", "1. 数据选择与预处理\n", "首先，在GEE中导入全国行政区域的矢量边界(本文选择的是湖北省的行政区域)。随后选择湖北省研究区域经纬度、时间以及数据集类型。\n", "本文选择2022年，覆盖了研究区域的MCD64A1数据集。其数据集信息特征可参考网址Earth Engine Data Catalog  |  Google for Developers"]}, {"cell_type": "code", "execution_count": 1, "id": "57e204c3-f49b-4bc3-8abc-41df3448e74a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (2656335114.py, line 1)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m// Define the location of the fire.\u001b[39m\n    ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["# Define the location of the fire.\n", "var lon = 114.35186990208535;\n", "var lat = 30.58233381161745;\n", "var zoom = 8;\n", "// 利用属性筛选湖北省的行政区边界\n", "var hb = province.filterMetadata('省', 'equals', '湖北省');\n", "print('湖北省矢量', hb);\n", "// Filter datasets to a specific date range:\n", "// start date of fire.\n", "var inYear = 2022;\n", "var inMonth = 1;\n", "var inDay = 1;\n", "var durationBA = 12; // in months\n", "// Date range for burned area.\n", "var startDateBA = ee.Date.fromYMD(inYear, inMonth, 1);\n", "var endDateBA = startDateBA.advance(durationBA, 'month');\n", "// MCD64A1 dateset\n", "var modisBurn = ee.ImageCollection('MODIS/061/MCD64A1')\n", "                  .filterBounds(hb)\n", "                  .filterDate(startDateBA, endDateBA)\n", "                  .select('BurnDate');\n"]}, {"cell_type": "code", "execution_count": null, "id": "d3120bd1-6377-4988-816c-b2e88aec7160", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}