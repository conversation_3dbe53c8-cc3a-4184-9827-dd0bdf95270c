{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 分块下载全球Satellite Embedding数据\n", "\n", "## 解决方案\n", "由于全球数据下载出现1列720行的问题，采用分块下载策略：\n", "- 将全球分成多个区域块（如6×3=18块）\n", "- 每块单独下载，避免投影问题\n", "- 后续可以拼接成完整的全球数据\n", "\n", "## 分块策略\n", "- 经度方向：6块，每块60°（-180到-120，-120到-60，...，120到180）\n", "- 纬度方向：3块，每块约57°（-85到-28，-28到29，29到85）\n", "- 总共18个区域块\n", "- 每块约240×227像素（0.25°分辨率）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n", "开始准备分块下载全球Satellite Embedding数据\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import os\n", "from pathlib import Path\n", "import time\n", "import numpy as np\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"开始准备分块下载全球Satellite Embedding数据\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["=== 设置分块下载参数 ===\n", "目标路径: H:\\satellite_embedding\\global_tiles\n", "年份: [2022]\n", "波段: ['A00']\n", "分块策略: 6×3 = 18块\n", "每块大小: 60.0° × 10.0°\n", "预期每块像素: 240×40\n", "已创建基础目录: H:\\satellite_embedding\\global_tiles\n"]}], "source": ["# 设置分块下载参数\n", "print(\"=== 设置分块下载参数 ===\")\n", "\n", "# 基本参数\n", "base_path = r\"H:\\satellite_embedding\\global_tiles\"\n", "start_year = 2022\n", "end_year = 2022\n", "years = list(range(start_year, end_year + 1))\n", "bands = ['A00']  # 先测试单个波段\n", "\n", "# 分块参数\n", "lon_blocks = 6  # 经度方向6块\n", "lat_blocks = 3  # 纬度方向3块\n", "\n", "# 全球范围\n", "global_bounds = [-180, 60, 180, 90]  # [min_lon, min_lat, max_lon, max_lat]\n", "\n", "# 计算每块的大小\n", "lon_step = (global_bounds[2] - global_bounds[0]) / lon_blocks  # 60度\n", "lat_step = (global_bounds[3] - global_bounds[1]) / lat_blocks  # 约57度\n", "\n", "print(f\"目标路径: {base_path}\")\n", "print(f\"年份: {years}\")\n", "print(f\"波段: {bands}\")\n", "print(f\"分块策略: {lon_blocks}×{lat_blocks} = {lon_blocks*lat_blocks}块\")\n", "print(f\"每块大小: {lon_step}° × {lat_step:.1f}°\")\n", "print(f\"预期每块像素: {int(lon_step/0.25)}×{int(lat_step/0.25)}\")\n", "\n", "# 创建基础目录\n", "Path(base_path).mkdir(parents=True, exist_ok=True)\n", "print(f\"已创建基础目录: {base_path}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 生成分块区域 ===\n", "块 tile_00_00: [-180.0, 60.0, -120.0, 70.0]\n", "块 tile_00_01: [-180.0, 70.0, -120.0, 80.0]\n", "块 tile_00_02: [-180.0, 80.0, -120.0, 90.0]\n", "块 tile_01_00: [-120.0, 60.0, -60.0, 70.0]\n", "块 tile_01_01: [-120.0, 70.0, -60.0, 80.0]\n", "块 tile_01_02: [-120.0, 80.0, -60.0, 90.0]\n", "块 tile_02_00: [-60.0, 60.0, 0.0, 70.0]\n", "块 tile_02_01: [-60.0, 70.0, 0.0, 80.0]\n", "块 tile_02_02: [-60.0, 80.0, 0.0, 90.0]\n", "块 tile_03_00: [0.0, 60.0, 60.0, 70.0]\n", "块 tile_03_01: [0.0, 70.0, 60.0, 80.0]\n", "块 tile_03_02: [0.0, 80.0, 60.0, 90.0]\n", "块 tile_04_00: [60.0, 60.0, 120.0, 70.0]\n", "块 tile_04_01: [60.0, 70.0, 120.0, 80.0]\n", "块 tile_04_02: [60.0, 80.0, 120.0, 90.0]\n", "块 tile_05_00: [120.0, 60.0, 180.0, 70.0]\n", "块 tile_05_01: [120.0, 70.0, 180.0, 80.0]\n", "块 tile_05_02: [120.0, 80.0, 180.0, 90.0]\n", "\n", "总共生成 18 个区域块\n"]}], "source": ["# 生成分块区域\n", "print(\"\\n=== 生成分块区域 ===\")\n", "\n", "tiles = []\n", "for i in range(lon_blocks):\n", "    for j in range(lat_blocks):\n", "        # 计算当前块的边界\n", "        min_lon = global_bounds[0] + i * lon_step\n", "        max_lon = global_bounds[0] + (i + 1) * lon_step\n", "        min_lat = global_bounds[1] + j * lat_step\n", "        max_lat = global_bounds[1] + (j + 1) * lat_step\n", "        \n", "        # 创建区域几何\n", "        tile_geometry = ee.Geometry.Rectangle([min_lon, min_lat, max_lon, max_lat])\n", "        \n", "        # 生成块标识\n", "        tile_id = f\"tile_{i:02d}_{j:02d}\"\n", "        \n", "        tiles.append({\n", "            'id': tile_id,\n", "            'geometry': tile_geometry,\n", "            'bounds': [min_lon, min_lat, max_lon, max_lat],\n", "            'lon_idx': i,\n", "            'lat_idx': j\n", "        })\n", "        \n", "        print(f\"块 {tile_id}: [{min_lon:.1f}, {min_lat:.1f}, {max_lon:.1f}, {max_lat:.1f}]\")\n", "\n", "print(f\"\\n总共生成 {len(tiles)} 个区域块\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 创建目录结构 ===\n", "已创建 2022 年目录及 18 个块子目录\n", "目录结构创建完成\n"]}], "source": ["# 创建目录结构\n", "print(\"\\n=== 创建目录结构 ===\")\n", "\n", "# 为每年创建目录\n", "year_paths = {}\n", "for year in years:\n", "    year_path = os.path.join(base_path, str(year))\n", "    Path(year_path).mkdir(parents=True, exist_ok=True)\n", "    year_paths[year] = year_path\n", "    \n", "    # 为每个块创建子目录\n", "    for tile in tiles:\n", "        tile_path = os.path.join(year_path, tile['id'])\n", "        Path(tile_path).mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"已创建 {year} 年目录及 {len(tiles)} 个块子目录\")\n", "\n", "print(f\"目录结构创建完成\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["分块下载函数已定义\n"]}], "source": ["# 分块下载函数\n", "def download_tile(year, band, tile, output_path):\n", "    \"\"\"\n", "    下载单个区域块的数据\n", "    \"\"\"\n", "    try:\n", "        print(f\"    下载块 {tile['id']}...\")\n", "        \n", "        # 加载数据\n", "        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "        start_date = ee.Date.fromYMD(year, 1, 1)\n", "        end_date = start_date.advance(1, 'year')\n", "        \n", "        # 过滤数据\n", "        filtered = embeddings.filter(ee.Filter.date(start_date, end_date))\n", "        image = filtered.mosaic().select([band])\n", "        \n", "        # 裁剪到当前块\n", "        clipped = image.clip(tile['geometry'])\n", "        \n", "        # 下载\n", "        geemap.download_ee_image(\n", "            image=clipped,\n", "            filename=output_path,\n", "            region=tile['geometry'],\n", "            crs=\"EPSG:4326\",\n", "            scale=27830,  # 约0.25度\n", "            file_per_band=False\n", "        )\n", "        \n", "        # 验证结果\n", "        if os.path.exists(output_path):\n", "            file_size = os.path.getsize(output_path)\n", "            print(f\"      ✓ 成功，大小: {file_size/1024:.1f} KB\")\n", "            \n", "            # 检查维度\n", "            try:\n", "                import rasterio\n", "                with rasterio.open(output_path) as src:\n", "                    print(f\"      维度: {src.width}×{src.height}\")\n", "                    if src.width > 1 and src.height > 1:\n", "                        print(f\"      ✓ 维度正常\")\n", "                    else:\n", "                        print(f\"      ⚠️ 维度异常\")\n", "            except:\n", "                pass\n", "            \n", "            return True, None\n", "        else:\n", "            return False, \"文件未创建\"\n", "            \n", "    except Exception as e:\n", "        return False, str(e)\n", "\n", "print(\"分块下载函数已定义\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 执行分块下载 ===\n", "开始下载 18 个区域块...\n", "\n", "--- 处理 2022 年 ---\n", "\n", "  波段 A00:\n", "    下载块 tile_00_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 5.6% (0/18)\n", "    下载块 tile_00_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 11.1% (0/18)\n", "    下载块 tile_00_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 16.7% (0/18)\n", "    下载块 tile_01_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 22.2% (0/18)\n", "    下载块 tile_01_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 27.8% (0/18)\n", "    下载块 tile_01_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 33.3% (0/18)\n", "    下载块 tile_02_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 38.9% (0/18)\n", "    下载块 tile_02_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 44.4% (0/18)\n", "    下载块 tile_02_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 50.0% (0/18)\n", "    下载块 tile_03_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 55.6% (0/18)\n", "    下载块 tile_03_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 61.1% (0/18)\n", "    下载块 tile_03_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 66.7% (0/18)\n", "    下载块 tile_04_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 72.2% (0/18)\n", "    下载块 tile_04_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 77.8% (0/18)\n", "    下载块 tile_04_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 83.3% (0/18)\n", "    下载块 tile_05_00...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 88.9% (0/18)\n", "    下载块 tile_05_01...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 94.4% (0/18)\n", "    下载块 tile_05_02...\n", "      ✗ 失败: BaseImage._prepare_for_export() got an unexpected keyword argument 'file_per_band'\n", "    进度: 100.0% (0/18)\n", "\n", "=== 分块下载完成 ===\n", "总用时: 0.6 分钟\n", "成功: 0 个块\n", "失败: 18 个块\n", "成功率: 0.0%\n"]}], "source": ["# 执行分块下载\n", "print(\"\\n=== 执行分块下载 ===\")\n", "\n", "total_tiles = len(tiles)\n", "completed = 0\n", "failed = 0\n", "failed_list = []\n", "\n", "print(f\"开始下载 {total_tiles} 个区域块...\")\n", "start_time = time.time()\n", "\n", "for year in years:\n", "    print(f\"\\n--- 处理 {year} 年 ---\")\n", "    year_path = year_paths[year]\n", "    \n", "    for band in bands:\n", "        print(f\"\\n  波段 {band}:\")\n", "        \n", "        for i, tile in enumerate(tiles):\n", "            # 构建输出路径\n", "            tile_dir = os.path.join(year_path, tile['id'])\n", "            filename = f\"{band}_{year}_{tile['id']}.tif\"\n", "            output_path = os.path.join(tile_dir, filename)\n", "            \n", "            # 检查是否已存在\n", "            if os.path.exists(output_path):\n", "                file_size = os.path.getsize(output_path)\n", "                if file_size > 1000:  # 大于1KB认为有效\n", "                    print(f\"    跳过 {tile['id']} (已存在)\")\n", "                    completed += 1\n", "                    continue\n", "            \n", "            # 执行下载\n", "            success, error = download_tile(year, band, tile, output_path)\n", "            \n", "            if success:\n", "                completed += 1\n", "            else:\n", "                failed += 1\n", "                failed_list.append((year, band, tile['id'], error))\n", "                print(f\"      ✗ 失败: {error}\")\n", "            \n", "            # 显示进度\n", "            progress = (completed + failed) / (total_tiles * len(bands) * len(years)) * 100\n", "            print(f\"    进度: {progress:.1f}% ({completed}/{total_tiles * len(bands) * len(years)})\")\n", "            \n", "            # 短暂延迟\n", "            time.sleep(2)\n", "\n", "end_time = time.time()\n", "total_time = end_time - start_time\n", "\n", "print(f\"\\n=== 分块下载完成 ===\")\n", "print(f\"总用时: {total_time/60:.1f} 分钟\")\n", "print(f\"成功: {completed} 个块\")\n", "print(f\"失败: {failed} 个块\")\n", "print(f\"成功率: {completed/(completed+failed)*100:.1f}%\" if (completed+failed) > 0 else \"0%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 验证分块下载结果\n", "print(\"\\n=== 验证分块下载结果 ===\")\n", "\n", "for year in years:\n", "    print(f\"\\n{year}年结果:\")\n", "    year_path = year_paths[year]\n", "    \n", "    for band in bands:\n", "        print(f\"  波段 {band}:\")\n", "        valid_tiles = 0\n", "        total_size = 0\n", "        \n", "        for tile in tiles:\n", "            tile_dir = os.path.join(year_path, tile['id'])\n", "            filename = f\"{band}_{year}_{tile['id']}.tif\"\n", "            file_path = os.path.join(tile_dir, filename)\n", "            \n", "            if os.path.exists(file_path):\n", "                file_size = os.path.getsize(file_path)\n", "                if file_size > 1000:\n", "                    valid_tiles += 1\n", "                    total_size += file_size\n", "                    \n", "                    # 检查第一个文件的详细信息\n", "                    if valid_tiles == 1:\n", "                        try:\n", "                            import rasterio\n", "                            with rasterio.open(file_path) as src:\n", "                                print(f\"    示例块 {tile['id']}: {src.width}×{src.height}, {file_size/1024:.1f}KB\")\n", "                        except:\n", "                            print(f\"    示例块 {tile['id']}: {file_size/1024:.1f}KB\")\n", "        \n", "        print(f\"    有效块数: {valid_tiles}/{len(tiles)}\")\n", "        print(f\"    总大小: {total_size/1024/1024:.1f} MB\")\n", "        \n", "        if valid_tiles == len(tiles):\n", "            print(f\"    ✓ 所有块下载完整\")\n", "        else:\n", "            print(f\"    ⚠️ 缺失 {len(tiles)-valid_tiles} 个块\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据拼接说明\n", "print(\"\\n=== 数据拼接说明 ===\")\n", "\n", "print(\"分块下载完成后，可以使用以下方法拼接成完整的全球数据:\")\n", "print(\"\")\n", "print(\"方法1: 使用GDAL命令行工具\")\n", "print(\"```bash\")\n", "print(\"# 创建虚拟栅格文件\")\n", "print(\"gdalbuildvrt global_mosaic.vrt tile_*/*.tif\")\n", "print(\"# 转换为单个文件\")\n", "print(\"gdal_translate global_mosaic.vrt global_complete.tif\")\n", "print(\"```\")\n", "print(\"\")\n", "print(\"方法2: 使用Python rasterio\")\n", "print(\"```python\")\n", "print(\"import rasterio\")\n", "print(\"from rasterio.merge import merge\")\n", "print(\"import glob\")\n", "print(\"\")\n", "print(\"# 获取所有块文件\")\n", "print(\"tile_files = glob.glob('*/tile_*/*.tif')\")\n", "print(\"src_files = [rasterio.open(f) for f in tile_files]\")\n", "print(\"\")\n", "print(\"# 拼接\")\n", "print(\"mosaic, out_trans = merge(src_files)\")\n", "print(\"\")\n", "print(\"# 保存\")\n", "print(\"with rasterio.open('global_complete.tif', 'w', **profile) as dst:\")\n", "print(\"    dst.write(mosaic)\")\n", "print(\"```\")\n", "print(\"\")\n", "print(\"拼接后的全球数据应该是 1440×680 像素（排除极地区域）\")\n", "print(\"或者根据实际下载的区域范围调整\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成拼接脚本\n", "print(\"\\n=== 生成拼接脚本 ===\")\n", "\n", "# 生成Python拼接脚本\n", "mosaic_script = os.path.join(base_path, \"mosaic_tiles.py\")\n", "\n", "with open(mosaic_script, 'w', encoding='utf-8') as f:\n", "    f.write('''#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "拼接分块下载的全球Satellite Embedding数据\n", "\"\"\"\n", "\n", "import rasterio\n", "from rasterio.merge import merge\n", "from rasterio.enums import Resampling\n", "import glob\n", "import os\n", "import numpy as np\n", "\n", "def mosaic_tiles(year, band, base_path, output_path):\n", "    \"\"\"\n", "    拼接指定年份和波段的所有块\n", "    \"\"\"\n", "    print(f\"拼接 {year}年 {band}波段...\")\n", "    \n", "    # 查找所有块文件\n", "    pattern = os.path.join(base_path, str(year), \"tile_*\", f\"{band}_{year}_tile_*.tif\")\n", "    tile_files = glob.glob(pattern)\n", "    \n", "    if not tile_files:\n", "        print(f\"  未找到块文件: {pattern}\")\n", "        return False\n", "    \n", "    print(f\"  找到 {len(tile_files)} 个块文件\")\n", "    \n", "    # 打开所有文件\n", "    src_files = []\n", "    for file in tile_files:\n", "        try:\n", "            src = rasterio.open(file)\n", "            src_files.append(src)\n", "        except Exception as e:\n", "            print(f\"  跳过损坏文件: {file} - {e}\")\n", "    \n", "    if not src_files:\n", "        print(f\"  没有有效的块文件\")\n", "        return False\n", "    \n", "    try:\n", "        # 执行拼接\n", "        mosaic, out_trans = merge(src_files, resampling=Resampling.nearest)\n", "        \n", "        # 获取输出参数\n", "        out_meta = src_files[0].meta.copy()\n", "        out_meta.update({\n", "            \"driver\": \"<PERSON>iff\",\n", "            \"height\": mosaic.shape[1],\n", "            \"width\": mosaic.shape[2],\n", "            \"transform\": out_trans,\n", "            \"compress\": \"lzw\"\n", "        })\n", "        \n", "        # 保存拼接结果\n", "        with rasterio.open(output_path, \"w\", **out_meta) as dest:\n", "            dest.write(mosaic)\n", "        \n", "        print(f\"  ✓ 拼接完成: {output_path}\")\n", "        print(f\"  输出尺寸: {mosaic.shape[2]}×{mosaic.shape[1]}\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"  ✗ 拼接失败: {e}\")\n", "        return False\n", "    \n", "    finally:\n", "        # 关闭所有文件\n", "        for src in src_files:\n", "            src.close()\n", "\n", "if __name__ == \"__main__\":\n", "    # 配置参数\n", "    base_path = r\"H:\\\\satellite_embedding\\\\global_tiles\"\n", "    output_dir = os.path.join(base_path, \"mosaics\")\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    years = [2022]\n", "    bands = ['A00']\n", "    \n", "    # 执行拼接\n", "    for year in years:\n", "        for band in bands:\n", "            output_file = os.path.join(output_dir, f\"{band}_{year}_global_mosaic.tif\")\n", "            mosaic_tiles(year, band, base_path, output_file)\n", "    \n", "    print(\"\\n拼接完成！\")\n", "''')\n", "\n", "print(f\"已生成拼接脚本: {mosaic_script}\")\n", "print(f\"运行方法: python {mosaic_script}\")\n", "\n", "# 生成批处理文件\n", "if os.name == 'nt':  # Windows\n", "    bat_script = os.path.join(base_path, \"run_mosaic.bat\")\n", "    with open(bat_script, 'w') as f:\n", "        f.write(f\"@echo off\\n\")\n", "        f.write(f\"cd /d \\\"{base_path}\\\"\\n\")\n", "        f.write(f\"python mosaic_tiles.py\\n\")\n", "        f.write(f\"pause\\n\")\n", "    print(f\"已生成批处理文件: {bat_script}\")\n", "\n", "print(f\"\\n=== 分块下载任务完成 ===\")\n", "print(f\"下一步: 运行拼接脚本合并所有块为完整的全球数据\")"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}