# Satellite Embedding 数据下载工具

## 概述

本工具用于批量下载Google Earth Engine上的Satellite Embedding V1数据，按年份和波段组织，重采样到0.25°分辨率。

## 文件说明

### 主要文件

1. **`download_satellite_embedding_025.ipynb`** - 完整的下载notebook
   - 交互式下载界面
   - 详细的进度显示
   - 下载验证和报告生成

2. **`test_download_satellite_embedding.ipynb`** - 测试下载功能
   - 下载少量数据进行测试
   - 验证下载功能是否正常

3. **`batch_download_satellite_embedding.py`** - 批处理下载脚本
   - 命令行批量下载
   - 支持断点续传
   - 自动重试机制

## 数据组织结构

```
H:\satellite_embedding\025\
├── 2017\
│   ├── A00_2017.tif
│   ├── A01_2017.tif
│   ├── ...
│   └── A63_2017.tif
├── 2018\
│   ├── A00_2018.tif
│   └── ...
├── ...
└── 2024\
    ├── A00_2024.tif
    └── ...
```

## 使用方法

### 方法1：使用Jupyter Notebook（推荐）

```bash
# 1. 先运行测试
jupyter notebook test_download_satellite_embedding.ipynb

# 2. 测试通过后运行完整下载
jupyter notebook download_satellite_embedding_025.ipynb
```

### 方法2：使用批处理脚本

```bash
# 直接运行批处理脚本
python batch_download_satellite_embedding.py
```

### 方法3：分步骤下载

如果数据量太大，可以分年份下载：

1. 修改notebook中的年份范围
2. 分别下载不同年份的数据
3. 最后合并到同一目录

## 配置参数

### 基本参数

- **研究区域**: 丝绸之路流域 (60°-120°E, 25°-50°N)
- **年份范围**: 2017-2024年
- **波段数量**: 64个 (A00-A63)
- **分辨率**: 0.25° (约27.8km)
- **投影**: EPSG:4326

### 可调整参数

```python
# 在notebook或脚本中修改这些参数
base_path = r"H:\satellite_embedding\025"  # 保存路径
start_year = 2017                          # 开始年份
end_year = 2024                           # 结束年份
scale = 27830                             # 分辨率
```

## 下载统计

### 预期数据量

- **总文件数**: 8年 × 64波段 = 512个TIF文件
- **预计大小**: 每个文件约1-10MB，总计约2-20GB
- **预计时间**: 约4-8小时（取决于网络速度）

### 进度监控

下载过程中会显示：
- 当前下载的年份和波段
- 完成进度百分比
- 已用时间和预计剩余时间
- 成功/失败统计

## 错误处理

### 常见问题

1. **网络连接问题**
   - 自动重试机制（最多3次）
   - 支持断点续传

2. **磁盘空间不足**
   - 检查H盘可用空间
   - 建议预留至少30GB空间

3. **Earth Engine配额限制**
   - 下载间隔1秒避免请求过快
   - 如遇限制可暂停后继续

### 故障恢复

如果下载中断：
1. 重新运行notebook或脚本
2. 程序会自动跳过已下载的文件
3. 从中断处继续下载

## 数据验证

### 自动验证

下载完成后会自动：
- 检查文件是否存在
- 验证文件大小（>1KB）
- 统计完整性
- 生成验证报告

### 手动验证

```python
# 检查特定文件
import rasterio
with rasterio.open('H:/satellite_embedding/025/2022/A00_2022.tif') as src:
    print(f"形状: {src.shape}")
    print(f"投影: {src.crs}")
    print(f"范围: {src.bounds}")
```

## 输出文件

### 数据文件

- **TIF文件**: 每个波段每年一个文件
- **命名规则**: `{波段}_{年份}.tif` (如 A00_2017.tif)

### 报告文件

- **download_progress.json**: 下载进度记录
- **download_report.txt**: 详细下载报告

## 性能优化

### 提高下载速度

1. **网络优化**
   - 使用稳定的网络连接
   - 确保代理设置正确

2. **并行下载**
   - 可以同时运行多个年份的下载
   - 注意不要超过Earth Engine限制

3. **存储优化**
   - 使用SSD硬盘提高写入速度
   - 确保足够的磁盘空间

### 资源管理

- **内存使用**: 约1-2GB
- **CPU使用**: 主要用于数据处理
- **网络带宽**: 持续下载需要稳定带宽

## 注意事项

1. **Earth Engine认证**
   - 确保已正确配置Earth Engine认证
   - 代理端口设置为33210

2. **存储路径**
   - 确保H盘存在且有足够空间
   - 路径中避免使用中文字符

3. **下载时间**
   - 建议在网络空闲时段下载
   - 避免在Earth Engine维护期间下载

4. **数据使用**
   - 下载的数据仅供研究使用
   - 遵守Google Earth Engine使用条款

## 技术支持

如遇问题请检查：
1. Earth Engine认证状态
2. 网络连接和代理设置
3. 磁盘空间和权限
4. 错误日志和报告文件

## 更新日志

- v1.0: 初始版本，支持基本下载功能
- v1.1: 添加断点续传和重试机制
- v1.2: 优化进度显示和错误处理
