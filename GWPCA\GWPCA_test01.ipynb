# 导入必要的库
import ee
import geemap

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")

# 创建交互式地图
Map = geemap.Map()

# 使用卫星底图
Map.add_basemap('SATELLITE')

print("地图已创建")

# 选择区域
geometry = ee.Geometry.Polygon([[
    [0, 55],
    [0, 20],
    [120, 20],
    [120, 55]
]])

Map.centerObject(geometry)
Map.addLayer(geometry, {'color': 'red'}, 'Selected Region', False)
print("已选择丝绸之路流域作为研究区域")

Map


# 准备卫星嵌入数据集
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')