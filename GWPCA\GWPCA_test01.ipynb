{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 使用卫星嵌入数据集进行地理加权主成分分析(GWPCA)\n", "\n", "## 算法目标\n", "利用地理加权主成分分析将卫星嵌入数据集降维到三维\n", "\n", "## 算法流程\n", "1. 通过geemap获取丝绸之路流域2017-01-01至2024-01-01的8年平均Satellite Embedding V1数据，数据重采样到0.25°\n", "2. 利用地理加权主成分将丝绸之路流域0.25°的多年平均卫星嵌入数据的64维降维到三维\n", "3. 可视化最后降维的结果（三个主成分各自以及累计解释方差空间分布）"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n", "所需库已导入\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.decomposition import PCA\n", "from sklearn.preprocessing import StandardScaler\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"所需库已导入\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图已创建\n"]}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "\n", "# 使用卫星底图\n", "Map.add_basemap('SATELLITE')\n", "\n", "print(\"地图已创建\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一步：定义丝绸之路流域研究区域"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已定义丝绸之路流域作为研究区域\n", "研究区域范围：经度 60°-120°E，纬度 25°-50°N\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6a604046e4a4ce2aca5bce5b43d83fe", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[40.470482694903446, 90], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=Se…"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义丝绸之路流域区域\n", "# 覆盖从中国西部到中亚、西亚的广大区域\n", "geometry = ee.Geometry.Polygon([[\n", "    [60, 25],   # 西南角\n", "    [60, 50],   # 西北角\n", "    [120, 50],  # 东北角\n", "    [120, 25]   # 东南角\n", "]])\n", "\n", "Map.centerObject(geometry, 4)\n", "Map.addLayer(geometry, {'color': 'red'}, '丝绸之路流域研究区域', False)\n", "print(\"已定义丝绸之路流域作为研究区域\")\n", "print(f\"研究区域范围：经度 60°-120°E，纬度 25°-50°N\")\n", "\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第二步：获取和预处理卫星嵌入数据"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 第一步：获取卫星嵌入数据 ===\n", "时间范围：2017-01-01 到 2024-01-01（8年）\n", "研究区域：丝绸之路流域\n", "已过滤卫星嵌入数据集\n", "- 时间过滤：2017-2024年\n", "- 空间过滤：丝绸之路流域\n", "已计算8年平均卫星嵌入数据\n", "- 波段数量：64个（A00到A63）\n", "- 每个波段代表嵌入空间的一个维度\n"]}], "source": ["# 准备卫星嵌入数据集\n", "print(\"\\n=== 第一步：获取卫星嵌入数据 ===\")\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "# 设置时间范围：2017-2024年（8年）\n", "start_date = ee.Date('2017-01-01')\n", "end_date = ee.Date('2024-01-01')\n", "\n", "print(f\"时间范围：2017-01-01 到 2024-01-01（8年）\")\n", "print(f\"研究区域：丝绸之路流域\")\n", "\n", "# 过滤数据集\n", "filtered_embeddings = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "print(f\"已过滤卫星嵌入数据集\")\n", "print(f\"- 时间过滤：2017-2024年\")\n", "print(f\"- 空间过滤：丝绸之路流域\")\n", "\n", "# 计算8年平均值\n", "embeddings_mean = filtered_embeddings.mean()\n", "\n", "print(f\"已计算8年平均卫星嵌入数据\")\n", "print(f\"- 波段数量：64个（A00到A63）\")\n", "print(f\"- 每个波段代表嵌入空间的一个维度\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 第二步：重采样到0.25°分辨率 ===\n", "已重采样到0.25°分辨率\n", "- 重采样方法：平均值\n", "- 目标投影：EPSG:4326\n", "- 目标分辨率：约0.25°\n"]}, {"ename": "EEException", "evalue": "Image.reduceResolution: The input to reduceResolution does not have a valid default projection. Use setDefaultProjection() first.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mHttpError\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:408\u001b[39m, in \u001b[36m_execute_cloud_call\u001b[39m\u001b[34m(call, num_retries)\u001b[39m\n\u001b[32m    407\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m408\u001b[39m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcall\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    409\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m googleapiclient.errors.HttpError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\googleapiclient\\_helpers.py:130\u001b[39m, in \u001b[36mpositional.<locals>.positional_decorator.<locals>.positional_wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    129\u001b[39m         logger.warning(message)\n\u001b[32m--> \u001b[39m\u001b[32m130\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\googleapiclient\\http.py:938\u001b[39m, in \u001b[36mHttpRequest.execute\u001b[39m\u001b[34m(self, http, num_retries)\u001b[39m\n\u001b[32m    937\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m resp.status >= \u001b[32m300\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m938\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m HttpError(resp, content, uri=\u001b[38;5;28mself\u001b[39m.uri)\n\u001b[32m    939\u001b[39m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.postproc(resp, content)\n", "\u001b[31mHttpError\u001b[39m: <HttpError 400 when requesting https://earthengine.googleapis.com/v1/projects/825775132399/maps?fields=name&alt=json returned \"Image.reduceResolution: The input to reduceResolution does not have a valid default projection. Use setDefaultProjection() first.\". Details: \"Image.reduceResolution: The input to reduceResolution does not have a valid default projection. Use setDefaultProjection() first.\">", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mEEException\u001b[39m                               Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 35\u001b[39m\n\u001b[32m     28\u001b[39m \u001b[38;5;66;03m# 可视化重采样后的数据（选择几个波段进行RGB显示）\u001b[39;00m\n\u001b[32m     29\u001b[39m vis_params = {\n\u001b[32m     30\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mmin\u001b[39m\u001b[33m'\u001b[39m: -\u001b[32m0.3\u001b[39m, \n\u001b[32m     31\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mmax\u001b[39m\u001b[33m'\u001b[39m: \u001b[32m0.3\u001b[39m, \n\u001b[32m     32\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mbands\u001b[39m\u001b[33m'\u001b[39m: [\u001b[33m'\u001b[39m\u001b[33mA01\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mA16\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mA32\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m     33\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m \u001b[43mMap\u001b[49m\u001b[43m.\u001b[49m\u001b[43madd<PERSON><PERSON><PERSON>\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[43m    \u001b[49m\u001b[43membeddings_resampled\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclip\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgeometry\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[32m     37\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvis_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[32m     38\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m重采样后的卫星嵌入数据（A01,A16,A32）\u001b[39;49m\u001b[33;43m'\u001b[39;49m\n\u001b[32m     39\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m已添加重采样后的数据到地图\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     42\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m- 显示波段：A01, A16, A32作为RGB\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\geemap\\geemap.py:444\u001b[39m, in \u001b[36mMap.add_ee_layer\u001b[39m\u001b[34m(self, ee_object, vis_params, name, shown, opacity)\u001b[39m\n\u001b[32m    439\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m has_plot_dropdown:\n\u001b[32m    440\u001b[39m             \u001b[38;5;28mself\u001b[39m._plot_dropdown_widget.options = \u001b[38;5;28mlist\u001b[39m(\n\u001b[32m    441\u001b[39m                 \u001b[38;5;28mself\u001b[39m.ee_raster_layers.keys()\n\u001b[32m    442\u001b[39m             )\n\u001b[32m--> \u001b[39m\u001b[32m444\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43madd_layer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mee_object\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvis_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshown\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopacity\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    446\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ee_object, (ee.Image, ee.ImageCollection)):\n\u001b[32m    447\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m has_plot_dropdown:\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\geemap\\core.py:1138\u001b[39m, in \u001b[36mMap.add_layer\u001b[39m\u001b[34m(self, ee_object, vis_params, name, shown, opacity)\u001b[39m\n\u001b[32m   1136\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ee_object, ee.ImageCollection):\n\u001b[32m   1137\u001b[39m     ee_object = ee_object.mosaic()\n\u001b[32m-> \u001b[39m\u001b[32m1138\u001b[39m tile_layer = \u001b[43mee_tile_layers\u001b[49m\u001b[43m.\u001b[49m\u001b[43mEELeafletTileLayer\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1139\u001b[39m \u001b[43m    \u001b[49m\u001b[43mee_object\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvis_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshown\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopacity\u001b[49m\n\u001b[32m   1140\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1142\u001b[39m \u001b[38;5;66;03m# Remove the layer if it already exists.\u001b[39;00m\n\u001b[32m   1143\u001b[39m \u001b[38;5;28mself\u001b[39m.remove(name)\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\geemap\\ee_tile_layers.py:206\u001b[39m, in \u001b[36mEELeafletTileLayer.__init__\u001b[39m\u001b[34m(self, ee_object, vis_params, name, shown, opacity, **kwargs)\u001b[39m\n\u001b[32m    192\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Initialize the ipyleaflet tile layer.\u001b[39;00m\n\u001b[32m    193\u001b[39m \n\u001b[32m    194\u001b[39m \u001b[33;03mArgs:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    203\u001b[39m \u001b[33;03m        number between 0 and 1. Defaults to 1.\u001b[39;00m\n\u001b[32m    204\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    205\u001b[39m \u001b[38;5;28mself\u001b[39m._ee_object = ee_object\n\u001b[32m--> \u001b[39m\u001b[32m206\u001b[39m \u001b[38;5;28mself\u001b[39m.url_format = \u001b[43m_get_tile_url_format\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    207\u001b[39m \u001b[43m    \u001b[49m\u001b[43mee_object\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_validate_vis_params\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvis_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    208\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    209\u001b[39m \u001b[38;5;28msuper\u001b[39m().\u001b[34m__init__\u001b[39m(\n\u001b[32m    210\u001b[39m     url=\u001b[38;5;28mself\u001b[39m.url_format,\n\u001b[32m    211\u001b[39m     attribution=\u001b[33m\"\u001b[39m\u001b[33mGoogle Earth Engine\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    216\u001b[39m     **kwargs,\n\u001b[32m    217\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\geemap\\ee_tile_layers.py:36\u001b[39m, in \u001b[36m_get_tile_url_format\u001b[39m\u001b[34m(ee_object, vis_params)\u001b[39m\n\u001b[32m     25\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Gets the tile URL format for an EE object.\u001b[39;00m\n\u001b[32m     26\u001b[39m \n\u001b[32m     27\u001b[39m \u001b[33;03mArgs:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     33\u001b[39m \u001b[33;03m    str: The tile URL format.\u001b[39;00m\n\u001b[32m     34\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     35\u001b[39m image = _ee_object_to_image(ee_object, vis_params)\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m map_id_dict = \u001b[43mee\u001b[49m\u001b[43m.\u001b[49m\u001b[43mImage\u001b[49m\u001b[43m(\u001b[49m\u001b[43mimage\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetMapId\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvis_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m map_id_dict[\u001b[33m\"\u001b[39m\u001b[33mtile_fetcher\u001b[39m\u001b[33m\"\u001b[39m].url_format\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\image.py:156\u001b[39m, in \u001b[36mImage.getMapId\u001b[39m\u001b[34m(self, vis_params)\u001b[39m\n\u001b[32m    154\u001b[39m vis_image, request = \u001b[38;5;28mself\u001b[39m._apply_visualization(vis_params)\n\u001b[32m    155\u001b[39m request[\u001b[33m'\u001b[39m\u001b[33mimage\u001b[39m\u001b[33m'\u001b[39m] = vis_image\n\u001b[32m--> \u001b[39m\u001b[32m156\u001b[39m response = \u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetMapId\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    157\u001b[39m response[\u001b[33m'\u001b[39m\u001b[33mimage\u001b[39m\u001b[33m'\u001b[39m] = \u001b[38;5;28mself\u001b[39m\n\u001b[32m    158\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:750\u001b[39m, in \u001b[36mgetMapId\u001b[39m\u001b[34m(params)\u001b[39m\n\u001b[32m    745\u001b[39m queryParams = {\n\u001b[32m    746\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mfields\u001b[39m\u001b[33m'\u001b[39m: \u001b[33m'\u001b[39m\u001b[33mname\u001b[39m\u001b[33m'\u001b[39m,\n\u001b[32m    747\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mbody\u001b[39m\u001b[33m'\u001b[39m: request,\n\u001b[32m    748\u001b[39m }\n\u001b[32m    749\u001b[39m _maybe_populate_workload_tag(queryParams)\n\u001b[32m--> \u001b[39m\u001b[32m750\u001b[39m result = \u001b[43m_execute_cloud_call\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    751\u001b[39m \u001b[43m    \u001b[49m\u001b[43m_get_cloud_projects\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    752\u001b[39m \u001b[43m    \u001b[49m\u001b[43m.\u001b[49m\u001b[43mmaps\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    753\u001b[39m \u001b[43m    \u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparent\u001b[49m\u001b[43m=\u001b[49m\u001b[43m_get_projects_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mqueryParams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    754\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    755\u001b[39m map_name = result[\u001b[33m'\u001b[39m\u001b[33mname\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m    756\u001b[39m url_format = \u001b[33m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m/tiles/\u001b[39m\u001b[38;5;132;01m{z}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m{x}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m{y}\u001b[39;00m\u001b[33m'\u001b[39m % (\n\u001b[32m    757\u001b[39m     _tile_base_url, _cloud_api_utils.VERSION, map_name)\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:410\u001b[39m, in \u001b[36m_execute_cloud_call\u001b[39m\u001b[34m(call, num_retries)\u001b[39m\n\u001b[32m    408\u001b[39m   \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m call.execute(num_retries=num_retries)\n\u001b[32m    409\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m googleapiclient.errors.HttpError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m410\u001b[39m   \u001b[38;5;28;01mraise\u001b[39;00m _translate_cloud_exception(e)\n", "\u001b[31mEEException\u001b[39m: Image.reduceResolution: The input to reduceResolution does not have a valid default projection. Use setDefaultProjection() first."]}], "source": ["# 重采样到0.25度分辨率\n", "print(\"\\n=== 第二步：重采样到0.25°分辨率 ===\")\n", "\n", "# 获取原始投影信息\n", "original_projection = embeddings_mean.select(0).projection()\n", "\n", "# 设置默认投影\n", "embeddings_with_projection = embeddings_mean.setDefaultProjection(original_projection)\n", "\n", "# 定义0.25度的投影\n", "target_projection = ee.Projection('EPSG:4326').atScale(27830)  # 约0.25度\n", "\n", "# 重采样数据\n", "embeddings_resampled = embeddings_with_projection \\\n", "    .reduceResolution(**{\n", "        'reducer': ee.Reducer.mean(),\n", "        'maxPixels': 1024\n", "    }) \\\n", "    .reproject(**{\n", "        'crs': target_projection\n", "    })\n", "\n", "print(f\"已重采样到0.25°分辨率\")\n", "print(f\"- 重采样方法：平均值\")\n", "print(f\"- 目标投影：EPSG:4326\")\n", "print(f\"- 目标分辨率：约0.25°\")\n", "\n", "# 可视化重采样后的数据（选择几个波段进行RGB显示）\n", "vis_params = {\n", "    'min': -0.3, \n", "    'max': 0.3, \n", "    'bands': ['A01', 'A16', 'A32']\n", "}\n", "\n", "Map.addLayer(\n", "    embeddings_resampled.clip(geometry), \n", "    vis_params, \n", "    '重采样后的卫星嵌入数据（A01,A16,A32）'\n", ")\n", "\n", "print(f\"已添加重采样后的数据到地图\")\n", "print(f\"- 显示波段：A01, A16, A32作为RGB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第三步：实现地理加权主成分分析(GWPCA)算法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 提取数据用于GWPCA分析\n", "print(\"\\n=== 第三步：准备GWPCA分析数据 ===\")\n", "\n", "# 使用简化的采样方法\n", "# 直接从图像中采样，避免复杂的网格创建\n", "print(f\"使用简化采样方法\")\n", "\n", "# 从重采样数据中随机采样\n", "samples = embeddings_resampled.sample(**{\n", "    'region': geometry,\n", "    'scale': 27830,  # 约0.25度\n", "    'numPixels': 500,  # 采样500个点\n", "    'seed': 42,\n", "    'dropNulls': True,\n", "    'geometries': True\n", "})\n", "\n", "# 获取样本数量\n", "sample_count = samples.size().getInfo()\n", "\n", "print(f\"已完成数据采样\")\n", "print(f\"- 采样方法：随机采样\")\n", "print(f\"- 采样尺度：约0.25°\")\n", "print(f\"- 样本数量：{sample_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将Earth Engine数据转换为Python数据结构\n", "print(\"\\n=== 第四步：数据转换和预处理 ===\")\n", "\n", "# 获取样本数据\n", "sample_data = samples.getInfo()\n", "features = sample_data['features']\n", "\n", "print(f\"已获取样本数据\")\n", "print(f\"- 样本数量：{len(features)}\")\n", "\n", "# 提取坐标和嵌入向量\n", "coordinates = []\n", "embeddings_data = []\n", "band_names = [f'A{i:02d}' for i in range(64)]  # A00 到 A63\n", "\n", "for feature in features:\n", "    # 获取坐标\n", "    coords = feature['geometry']['coordinates']\n", "    coordinates.append([coords[0], coords[1]])\n", "    \n", "    # 获取嵌入向量\n", "    properties = feature['properties']\n", "    embedding_vector = []\n", "    \n", "    # 检查是否有缺失值\n", "    has_null = False\n", "    for band in band_names:\n", "        if band in properties and properties[band] is not None:\n", "            embedding_vector.append(properties[band])\n", "        else:\n", "            has_null = True\n", "            break\n", "    \n", "    # 只保留完整的样本\n", "    if not has_null and len(embedding_vector) == 64:\n", "        embeddings_data.append(embedding_vector)\n", "    else:\n", "        coordinates.pop()  # 移除对应的坐标\n", "\n", "# 转换为numpy数组\n", "coordinates = np.array(coordinates)\n", "embeddings_data = np.array(embeddings_data)\n", "\n", "print(f\"数据预处理完成\")\n", "print(f\"- 有效样本数量：{len(embeddings_data)}\")\n", "print(f\"- 嵌入向量维度：{embeddings_data.shape[1]}\")\n", "print(f\"- 坐标范围：经度 {coordinates[:, 0].min():.2f} - {coordinates[:, 0].max():.2f}\")\n", "print(f\"- 坐标范围：纬度 {coordinates[:, 1].min():.2f} - {coordinates[:, 1].max():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 实现地理加权主成分分析(GWPCA)核心算法\n", "print(\"\\n=== 第五步：实现GWPCA算法 ===\")\n", "\n", "def gaussian_kernel(distances, bandwidth):\n", "    \"\"\"\n", "    高斯核函数计算空间权重\n", "    \"\"\"\n", "    return np.exp(-(distances**2) / (2 * bandwidth**2))\n", "\n", "def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):\n", "    \"\"\"\n", "    在指定位置执行地理加权主成分分析\n", "    \n", "    参数:\n", "        target_coords: 目标位置坐标 [lon, lat]\n", "        all_coords: 所有样本坐标 (n_samples, 2)\n", "        all_data: 所有样本数据 (n_samples, n_features)\n", "        bandwidth: 带宽参数\n", "        n_components: 主成分数量\n", "    \n", "    返回:\n", "        components: 主成分\n", "        explained_variance_ratio: 解释方差比\n", "        transformed_data: 降维后的数据\n", "    \"\"\"\n", "    # 计算距离\n", "    distances = cdist([target_coords], all_coords, metric='euclidean')[0]\n", "    \n", "    # 计算权重\n", "    weights = gaussian_kernel(distances, bandwidth)\n", "    \n", "    # 权重归一化\n", "    weights = weights / np.sum(weights)\n", "    \n", "    # 加权数据标准化\n", "    weighted_mean = np.average(all_data, weights=weights, axis=0)\n", "    centered_data = all_data - weighted_mean\n", "    \n", "    # 计算加权协方差矩阵\n", "    weighted_cov = np.cov(centered_data.T, aweights=weights)\n", "    \n", "    # 特征值分解\n", "    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)\n", "    \n", "    # 按特征值降序排列\n", "    idx = np.argsort(eigenvalues)[::-1]\n", "    eigenvalues = eigenvalues[idx]\n", "    eigenvectors = eigenvectors[:, idx]\n", "    \n", "    # 选择前n_components个主成分\n", "    components = eigenvectors[:, :n_components]\n", "    explained_variance = eigenvalues[:n_components]\n", "    explained_variance_ratio = explained_variance / np.sum(eigenvalues)\n", "    \n", "    # 变换目标位置的数据\n", "    target_idx = np.argmin(distances)\n", "    target_data = all_data[target_idx] - weighted_mean\n", "    transformed_data = np.dot(target_data, components)\n", "    \n", "    return components, explained_variance_ratio, transformed_data\n", "\n", "print(\"GWPCA核心算法已定义\")\n", "print(\"- 使用高斯核函数计算空间权重\")\n", "print(\"- 支持局部加权协方差矩阵计算\")\n", "print(\"- 输出前3个主成分\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行GWPCA分析\n", "print(\"\\n=== 第六步：执行GWPCA分析 ===\")\n", "\n", "# 设置带宽参数（以度为单位）\n", "bandwidth = 2.0  # 2度的带宽\n", "\n", "print(f\"GWPCA参数设置：\")\n", "print(f\"- 带宽：{bandwidth}度\")\n", "print(f\"- 主成分数量：3\")\n", "print(f\"- 核函数：高斯核\")\n", "\n", "# 为每个位置执行GWPCA\n", "n_samples = len(coordinates)\n", "pc1_values = np.zeros(n_samples)\n", "pc2_values = np.zeros(n_samples)\n", "pc3_values = np.zeros(n_samples)\n", "explained_var_ratios = np.zeros((n_samples, 3))\n", "\n", "print(f\"\\n开始处理 {n_samples} 个位置...\")\n", "\n", "# 批量处理（为了提高效率，可以考虑并行处理）\n", "for i, target_coord in enumerate(coordinates):\n", "    if i % 50 == 0:  # 每50个点显示进度\n", "        print(f\"处理进度: {i+1}/{n_samples} ({(i+1)/n_samples*100:.1f}%)\")\n", "    \n", "    try:\n", "        components, var_ratios, transformed = gwpca_at_location(\n", "            target_coord, coordinates, embeddings_data, bandwidth, n_components=3\n", "        )\n", "        \n", "        # 存储结果\n", "        pc1_values[i] = transformed[0]\n", "        pc2_values[i] = transformed[1] \n", "        pc3_values[i] = transformed[2]\n", "        explained_var_ratios[i] = var_ratios\n", "        \n", "    except Exception as e:\n", "        print(f\"位置 {i} 处理失败: {e}\")\n", "        # 使用默认值\n", "        pc1_values[i] = 0\n", "        pc2_values[i] = 0\n", "        pc3_values[i] = 0\n", "        explained_var_ratios[i] = [0, 0, 0]\n", "\n", "print(f\"\\nGWPCA分析完成！\")\n", "print(f\"- 成功处理位置数：{n_samples}\")\n", "print(f\"- 第一主成分范围：{pc1_values.min():.3f} - {pc1_values.max():.3f}\")\n", "print(f\"- 第二主成分范围：{pc2_values.min():.3f} - {pc2_values.max():.3f}\")\n", "print(f\"- 第三主成分范围：{pc3_values.min():.3f} - {pc3_values.max():.3f}\")\n", "print(f\"- 平均解释方差比：PC1={explained_var_ratios[:, 0].mean():.3f}, PC2={explained_var_ratios[:, 1].mean():.3f}, PC3={explained_var_ratios[:, 2].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第四步：结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建结果可视化\n", "print(\"\\n=== 第七步：结果可视化 ===\")\n", "\n", "# 创建新的地图用于显示结果\n", "Map_results = geemap.Map()\n", "Map_results.add_basemap('SATELLITE')\n", "Map_results.centerObject(geometry, 4)\n", "\n", "# 添加研究区域\n", "Map_results.addLayer(geometry, {'color': 'red'}, '研究区域', False)\n", "\n", "print(\"已创建结果可视化地图\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将结果转换为Earth Engine图像进行可视化\n", "def create_result_image(coords, values, geometry, band_name):\n", "    \"\"\"\n", "    将点数据转换为栅格图像\n", "    \"\"\"\n", "    # 创建特征集合\n", "    features = []\n", "    for i, (coord, value) in enumerate(zip(coords, values)):\n", "        point = ee.Geometry.Point([coord[0], coord[1]])\n", "        feature = ee.Feature(point, {band_name: float(value)})\n", "        features.append(feature)\n", "    \n", "    feature_collection = ee.FeatureCollection(features)\n", "    \n", "    # 插值生成栅格\n", "    # 使用kriging插值或简单的距离加权插值\n", "    image = feature_collection.reduceToImage(**{\n", "        'properties': [band_name],\n", "        'reducer': ee.Reducer.mean()\n", "    }).rename(band_name)\n", "    \n", "    return image\n", "\n", "# 创建主成分图像\n", "pc1_image = create_result_image(coordinates, pc1_values, geometry, 'PC1')\n", "pc2_image = create_result_image(coordinates, pc2_values, geometry, 'PC2')\n", "pc3_image = create_result_image(coordinates, pc3_values, geometry, 'PC3')\n", "\n", "# 创建解释方差图像\n", "var_ratio_1 = create_result_image(coordinates, explained_var_ratios[:, 0], geometry, 'VarRatio1')\n", "var_ratio_2 = create_result_image(coordinates, explained_var_ratios[:, 1], geometry, 'VarRatio2')\n", "var_ratio_3 = create_result_image(coordinates, explained_var_ratios[:, 2], geometry, 'VarRatio3')\n", "\n", "# 计算累计解释方差\n", "cumulative_var = explained_var_ratios[:, 0] + explained_var_ratios[:, 1] + explained_var_ratios[:, 2]\n", "cumulative_var_image = create_result_image(coordinates, cumulative_var, geometry, 'CumulativeVar')\n", "\n", "print(\"已创建结果图像\")\n", "print(\"- 第一主成分图像\")\n", "print(\"- 第二主成分图像\")\n", "print(\"- 第三主成分图像\")\n", "print(\"- 各主成分解释方差图像\")\n", "print(\"- 累计解释方差图像\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 添加主成分可视化图层\n", "print(\"\\n=== 添加主成分可视化图层 ===\")\n", "\n", "# 第一主成分可视化\n", "pc1_vis = {\n", "    'min': float(pc1_values.min()),\n", "    'max': float(pc1_values.max()),\n", "    'palette': ['blue', 'cyan', 'yellow', 'red']\n", "}\n", "\n", "Map_results.addLayer(\n", "    pc1_image.clip(geometry),\n", "    pc1_vis,\n", "    '第一主成分 (PC1)'\n", ")\n", "\n", "# 第二主成分可视化\n", "pc2_vis = {\n", "    'min': float(pc2_values.min()),\n", "    'max': float(pc2_values.max()),\n", "    'palette': ['purple', 'blue', 'green', 'yellow']\n", "}\n", "\n", "Map_results.addLayer(\n", "    pc2_image.clip(geometry),\n", "    pc2_vis,\n", "    '第二主成分 (PC2)',\n", "    False\n", ")\n", "\n", "# 第三主成分可视化\n", "pc3_vis = {\n", "    'min': float(pc3_values.min()),\n", "    'max': float(pc3_values.max()),\n", "    'palette': ['darkred', 'red', 'orange', 'yellow']\n", "}\n", "\n", "Map_results.addLayer(\n", "    pc3_image.clip(geometry),\n", "    pc3_vis,\n", "    '第三主成分 (PC3)',\n", "    False\n", ")\n", "\n", "print(\"已添加三个主成分可视化图层\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 添加解释方差可视化图层\n", "print(\"\\n=== 添加解释方差可视化图层 ===\")\n", "\n", "# 第一主成分解释方差\n", "var1_vis = {\n", "    'min': 0,\n", "    'max': float(explained_var_ratios[:, 0].max()),\n", "    'palette': ['white', 'lightblue', 'blue', 'darkblue']\n", "}\n", "\n", "Map_results.addLayer(\n", "    var_ratio_1.clip(geometry),\n", "    var1_vis,\n", "    'PC1解释方差比',\n", "    False\n", ")\n", "\n", "# 第二主成分解释方差\n", "var2_vis = {\n", "    'min': 0,\n", "    'max': float(explained_var_ratios[:, 1].max()),\n", "    'palette': ['white', 'lightgreen', 'green', 'darkgreen']\n", "}\n", "\n", "Map_results.addLayer(\n", "    var_ratio_2.clip(geometry),\n", "    var2_vis,\n", "    'PC2解释方差比',\n", "    False\n", ")\n", "\n", "# 第三主成分解释方差\n", "var3_vis = {\n", "    'min': 0,\n", "    'max': float(explained_var_ratios[:, 2].max()),\n", "    'palette': ['white', 'lightyellow', 'orange', 'red']\n", "}\n", "\n", "Map_results.addLayer(\n", "    var_ratio_3.clip(geometry),\n", "    var3_vis,\n", "    'PC3解释方差比',\n", "    False\n", ")\n", "\n", "# 累计解释方差\n", "cumvar_vis = {\n", "    'min': 0,\n", "    'max': float(cumulative_var.max()),\n", "    'palette': ['black', 'purple', 'blue', 'cyan', 'green', 'yellow', 'red']\n", "}\n", "\n", "Map_results.addLayer(\n", "    cumulative_var_image.clip(geometry),\n", "    cumvar_vis,\n", "    '累计解释方差比',\n", "    False\n", ")\n", "\n", "print(\"已添加解释方差可视化图层\")\n", "print(\"- PC1解释方差比\")\n", "print(\"- PC2解释方差比\")\n", "print(\"- PC3解释方差比\")\n", "print(\"- 累计解释方差比\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示结果地图\n", "print(\"\\n=== 显示GWPCA分析结果 ===\")\n", "\n", "Map_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成统计报告\n", "print(\"\\n=== GWPCA分析统计报告 ===\")\n", "print(\"\\n1. 数据概况:\")\n", "print(f\"   - 研究区域：丝绸之路流域 (60°-120°E, 25°-50°N)\")\n", "print(f\"   - 时间范围：2017-2024年（8年平均）\")\n", "print(f\"   - 空间分辨率：0.25°\")\n", "print(f\"   - 有效样本数：{len(embeddings_data)}\")\n", "print(f\"   - 原始维度：64维\")\n", "print(f\"   - 降维后维度：3维\")\n", "\n", "print(\"\\n2. 主成分统计:\")\n", "print(f\"   第一主成分 (PC1):\")\n", "print(f\"     - 数值范围：{pc1_values.min():.4f} - {pc1_values.max():.4f}\")\n", "print(f\"     - 平均解释方差比：{explained_var_ratios[:, 0].mean():.4f}\")\n", "print(f\"     - 标准差：{pc1_values.std():.4f}\")\n", "\n", "print(f\"   第二主成分 (PC2):\")\n", "print(f\"     - 数值范围：{pc2_values.min():.4f} - {pc2_values.max():.4f}\")\n", "print(f\"     - 平均解释方差比：{explained_var_ratios[:, 1].mean():.4f}\")\n", "print(f\"     - 标准差：{pc2_values.std():.4f}\")\n", "\n", "print(f\"   第三主成分 (PC3):\")\n", "print(f\"     - 数值范围：{pc3_values.min():.4f} - {pc3_values.max():.4f}\")\n", "print(f\"     - 平均解释方差比：{explained_var_ratios[:, 2].mean():.4f}\")\n", "print(f\"     - 标准差：{pc3_values.std():.4f}\")\n", "\n", "print(\"\\n3. 总体解释方差:\")\n", "total_explained = explained_var_ratios.sum(axis=1).mean()\n", "print(f\"   - 前三个主成分累计解释方差比：{total_explained:.4f}\")\n", "print(f\"   - 信息保留率：{total_explained*100:.2f}%\")\n", "\n", "print(\"\\n4. GWPCA参数:\")\n", "print(f\"   - 空间权重函数：高斯核\")\n", "print(f\"   - 带宽参数：{bandwidth}度\")\n", "print(f\"   - 降维目标：3个主成分\")\n", "\n", "print(\"\\n=== 分析完成 ===\")\n", "print(\"\\n可视化图层说明:\")\n", "print(\"- 第一主成分 (PC1)：显示主要的空间变异模式\")\n", "print(\"- 第二主成分 (PC2)：显示次要的空间变异模式\")\n", "print(\"- 第三主成分 (PC3)：显示第三重要的空间变异模式\")\n", "print(\"- 解释方差比图层：显示各主成分在不同位置的重要性\")\n", "print(\"- 累计解释方差比：显示降维后信息保留的空间分布\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}