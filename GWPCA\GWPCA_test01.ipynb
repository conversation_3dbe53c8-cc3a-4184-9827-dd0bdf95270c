# 导入必要的库
import ee
import geemap
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")
print("所需库已导入")

# 创建交互式地图
Map = geemap.Map()

# 使用卫星底图
Map.add_basemap('SATELLITE')

print("地图已创建")

# 定义丝绸之路流域区域
# 覆盖从中国西部到中亚、西亚的广大区域
geometry = ee.Geometry.Polygon([[
    [60, 25],   # 西南角
    [60, 50],   # 西北角
    [120, 50],  # 东北角
    [120, 25]   # 东南角
]])

Map.centerObject(geometry, 4)
Map.addLayer(geometry, {'color': 'red'}, '丝绸之路流域研究区域', False)
print("已定义丝绸之路流域作为研究区域")
print(f"研究区域范围：经度 60°-120°E，纬度 25°-50°N")

Map

# 准备卫星嵌入数据集
print("\n=== 第一步：获取卫星嵌入数据 ===")
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

# 设置时间范围：2017-2024年（8年）
start_date = ee.Date('2017-01-01')
end_date = ee.Date('2024-01-01')

print(f"时间范围：2017-01-01 到 2024-01-01（8年）")
print(f"研究区域：丝绸之路流域")

# 过滤数据集
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

print(f"已过滤卫星嵌入数据集")
print(f"- 时间过滤：2017-2024年")
print(f"- 空间过滤：丝绸之路流域")

# 计算8年平均值
embeddings_mean = filtered_embeddings.mean()

print(f"已计算8年平均卫星嵌入数据")
print(f"- 波段数量：64个（A00到A63）")
print(f"- 每个波段代表嵌入空间的一个维度")

# 重采样到0.25度分辨率
print("\n=== 第二步：重采样到0.25°分辨率 ===")

# 获取原始投影信息
original_projection = embeddings_mean.select(0).projection()

# 设置默认投影
embeddings_with_projection = embeddings_mean.setDefaultProjection(original_projection)

# 定义0.25度的投影
target_projection = ee.Projection('EPSG:4326').atScale(27830)  # 约0.25度

# 重采样数据
embeddings_resampled = embeddings_with_projection \
    .reduceResolution(**{
        'reducer': ee.Reducer.mean(),
        'maxPixels': 1024
    }) \
    .reproject(**{
        'crs': target_projection
    })

print(f"已重采样到0.25°分辨率")
print(f"- 重采样方法：平均值")
print(f"- 目标投影：EPSG:4326")
print(f"- 目标分辨率：约0.25°")

# 可视化重采样后的数据（选择几个波段进行RGB显示）
vis_params = {
    'min': -0.3, 
    'max': 0.3, 
    'bands': ['A01', 'A16', 'A32']
}

Map.addLayer(
    embeddings_resampled.clip(geometry), 
    vis_params, 
    '重采样后的卫星嵌入数据（A01,A16,A32）'
)

print(f"已添加重采样后的数据到地图")
print(f"- 显示波段：A01, A16, A32作为RGB")

# 提取数据用于GWPCA分析
print("\n=== 第三步：准备GWPCA分析数据 ===")

# 使用简化的采样方法
# 直接从图像中采样，避免复杂的网格创建
print(f"使用简化采样方法")

# 从重采样数据中随机采样
samples = embeddings_resampled.sample(**{
    'region': geometry,
    'scale': 27830,  # 约0.25度
    'numPixels': 500,  # 采样500个点
    'seed': 42,
    'dropNulls': True,
    'geometries': True
})

# 获取样本数量
sample_count = samples.size().getInfo()

print(f"已完成数据采样")
print(f"- 采样方法：随机采样")
print(f"- 采样尺度：约0.25°")
print(f"- 样本数量：{sample_count}")

# 将Earth Engine数据转换为Python数据结构
print("\n=== 第四步：数据转换和预处理 ===")

# 获取样本数据
sample_data = samples.getInfo()
features = sample_data['features']

print(f"已获取样本数据")
print(f"- 样本数量：{len(features)}")

# 提取坐标和嵌入向量
coordinates = []
embeddings_data = []
band_names = [f'A{i:02d}' for i in range(64)]  # A00 到 A63

for feature in features:
    # 获取坐标
    coords = feature['geometry']['coordinates']
    coordinates.append([coords[0], coords[1]])
    
    # 获取嵌入向量
    properties = feature['properties']
    embedding_vector = []
    
    # 检查是否有缺失值
    has_null = False
    for band in band_names:
        if band in properties and properties[band] is not None:
            embedding_vector.append(properties[band])
        else:
            has_null = True
            break
    
    # 只保留完整的样本
    if not has_null and len(embedding_vector) == 64:
        embeddings_data.append(embedding_vector)
    else:
        coordinates.pop()  # 移除对应的坐标

# 转换为numpy数组
coordinates = np.array(coordinates)
embeddings_data = np.array(embeddings_data)

print(f"数据预处理完成")
print(f"- 有效样本数量：{len(embeddings_data)}")
print(f"- 嵌入向量维度：{embeddings_data.shape[1]}")
print(f"- 坐标范围：经度 {coordinates[:, 0].min():.2f} - {coordinates[:, 0].max():.2f}")
print(f"- 坐标范围：纬度 {coordinates[:, 1].min():.2f} - {coordinates[:, 1].max():.2f}")

# 实现地理加权主成分分析(GWPCA)核心算法
print("\n=== 第五步：实现GWPCA算法 ===")

def gaussian_kernel(distances, bandwidth):
    """
    高斯核函数计算空间权重
    """
    return np.exp(-(distances**2) / (2 * bandwidth**2))

def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):
    """
    在指定位置执行地理加权主成分分析
    
    参数:
        target_coords: 目标位置坐标 [lon, lat]
        all_coords: 所有样本坐标 (n_samples, 2)
        all_data: 所有样本数据 (n_samples, n_features)
        bandwidth: 带宽参数
        n_components: 主成分数量
    
    返回:
        components: 主成分
        explained_variance_ratio: 解释方差比
        transformed_data: 降维后的数据
    """
    # 计算距离
    distances = cdist([target_coords], all_coords, metric='euclidean')[0]
    
    # 计算权重
    weights = gaussian_kernel(distances, bandwidth)
    
    # 权重归一化
    weights = weights / np.sum(weights)
    
    # 加权数据标准化
    weighted_mean = np.average(all_data, weights=weights, axis=0)
    centered_data = all_data - weighted_mean
    
    # 计算加权协方差矩阵
    weighted_cov = np.cov(centered_data.T, aweights=weights)
    
    # 特征值分解
    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)
    
    # 按特征值降序排列
    idx = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # 选择前n_components个主成分
    components = eigenvectors[:, :n_components]
    explained_variance = eigenvalues[:n_components]
    explained_variance_ratio = explained_variance / np.sum(eigenvalues)
    
    # 变换目标位置的数据
    target_idx = np.argmin(distances)
    target_data = all_data[target_idx] - weighted_mean
    transformed_data = np.dot(target_data, components)
    
    return components, explained_variance_ratio, transformed_data

print("GWPCA核心算法已定义")
print("- 使用高斯核函数计算空间权重")
print("- 支持局部加权协方差矩阵计算")
print("- 输出前3个主成分")

# 执行GWPCA分析
print("\n=== 第六步：执行GWPCA分析 ===")

# 设置带宽参数（以度为单位）
bandwidth = 2.0  # 2度的带宽

print(f"GWPCA参数设置：")
print(f"- 带宽：{bandwidth}度")
print(f"- 主成分数量：3")
print(f"- 核函数：高斯核")

# 为每个位置执行GWPCA
n_samples = len(coordinates)
pc1_values = np.zeros(n_samples)
pc2_values = np.zeros(n_samples)
pc3_values = np.zeros(n_samples)
explained_var_ratios = np.zeros((n_samples, 3))

print(f"\n开始处理 {n_samples} 个位置...")

# 批量处理（为了提高效率，可以考虑并行处理）
for i, target_coord in enumerate(coordinates):
    if i % 50 == 0:  # 每50个点显示进度
        print(f"处理进度: {i+1}/{n_samples} ({(i+1)/n_samples*100:.1f}%)")
    
    try:
        components, var_ratios, transformed = gwpca_at_location(
            target_coord, coordinates, embeddings_data, bandwidth, n_components=3
        )
        
        # 存储结果
        pc1_values[i] = transformed[0]
        pc2_values[i] = transformed[1] 
        pc3_values[i] = transformed[2]
        explained_var_ratios[i] = var_ratios
        
    except Exception as e:
        print(f"位置 {i} 处理失败: {e}")
        # 使用默认值
        pc1_values[i] = 0
        pc2_values[i] = 0
        pc3_values[i] = 0
        explained_var_ratios[i] = [0, 0, 0]

print(f"\nGWPCA分析完成！")
print(f"- 成功处理位置数：{n_samples}")
print(f"- 第一主成分范围：{pc1_values.min():.3f} - {pc1_values.max():.3f}")
print(f"- 第二主成分范围：{pc2_values.min():.3f} - {pc2_values.max():.3f}")
print(f"- 第三主成分范围：{pc3_values.min():.3f} - {pc3_values.max():.3f}")
print(f"- 平均解释方差比：PC1={explained_var_ratios[:, 0].mean():.3f}, PC2={explained_var_ratios[:, 1].mean():.3f}, PC3={explained_var_ratios[:, 2].mean():.3f}")

# 创建结果可视化
print("\n=== 第七步：结果可视化 ===")

# 创建新的地图用于显示结果
Map_results = geemap.Map()
Map_results.add_basemap('SATELLITE')
Map_results.centerObject(geometry, 4)

# 添加研究区域
Map_results.addLayer(geometry, {'color': 'red'}, '研究区域', False)

print("已创建结果可视化地图")

# 将结果转换为Earth Engine图像进行可视化
def create_result_image(coords, values, geometry, band_name):
    """
    将点数据转换为栅格图像
    """
    # 创建特征集合
    features = []
    for i, (coord, value) in enumerate(zip(coords, values)):
        point = ee.Geometry.Point([coord[0], coord[1]])
        feature = ee.Feature(point, {band_name: float(value)})
        features.append(feature)
    
    feature_collection = ee.FeatureCollection(features)
    
    # 插值生成栅格
    # 使用kriging插值或简单的距离加权插值
    image = feature_collection.reduceToImage(**{
        'properties': [band_name],
        'reducer': ee.Reducer.mean()
    }).rename(band_name)
    
    return image

# 创建主成分图像
pc1_image = create_result_image(coordinates, pc1_values, geometry, 'PC1')
pc2_image = create_result_image(coordinates, pc2_values, geometry, 'PC2')
pc3_image = create_result_image(coordinates, pc3_values, geometry, 'PC3')

# 创建解释方差图像
var_ratio_1 = create_result_image(coordinates, explained_var_ratios[:, 0], geometry, 'VarRatio1')
var_ratio_2 = create_result_image(coordinates, explained_var_ratios[:, 1], geometry, 'VarRatio2')
var_ratio_3 = create_result_image(coordinates, explained_var_ratios[:, 2], geometry, 'VarRatio3')

# 计算累计解释方差
cumulative_var = explained_var_ratios[:, 0] + explained_var_ratios[:, 1] + explained_var_ratios[:, 2]
cumulative_var_image = create_result_image(coordinates, cumulative_var, geometry, 'CumulativeVar')

print("已创建结果图像")
print("- 第一主成分图像")
print("- 第二主成分图像")
print("- 第三主成分图像")
print("- 各主成分解释方差图像")
print("- 累计解释方差图像")

# 添加主成分可视化图层
print("\n=== 添加主成分可视化图层 ===")

# 第一主成分可视化
pc1_vis = {
    'min': float(pc1_values.min()),
    'max': float(pc1_values.max()),
    'palette': ['blue', 'cyan', 'yellow', 'red']
}

Map_results.addLayer(
    pc1_image.clip(geometry),
    pc1_vis,
    '第一主成分 (PC1)'
)

# 第二主成分可视化
pc2_vis = {
    'min': float(pc2_values.min()),
    'max': float(pc2_values.max()),
    'palette': ['purple', 'blue', 'green', 'yellow']
}

Map_results.addLayer(
    pc2_image.clip(geometry),
    pc2_vis,
    '第二主成分 (PC2)',
    False
)

# 第三主成分可视化
pc3_vis = {
    'min': float(pc3_values.min()),
    'max': float(pc3_values.max()),
    'palette': ['darkred', 'red', 'orange', 'yellow']
}

Map_results.addLayer(
    pc3_image.clip(geometry),
    pc3_vis,
    '第三主成分 (PC3)',
    False
)

print("已添加三个主成分可视化图层")

# 添加解释方差可视化图层
print("\n=== 添加解释方差可视化图层 ===")

# 第一主成分解释方差
var1_vis = {
    'min': 0,
    'max': float(explained_var_ratios[:, 0].max()),
    'palette': ['white', 'lightblue', 'blue', 'darkblue']
}

Map_results.addLayer(
    var_ratio_1.clip(geometry),
    var1_vis,
    'PC1解释方差比',
    False
)

# 第二主成分解释方差
var2_vis = {
    'min': 0,
    'max': float(explained_var_ratios[:, 1].max()),
    'palette': ['white', 'lightgreen', 'green', 'darkgreen']
}

Map_results.addLayer(
    var_ratio_2.clip(geometry),
    var2_vis,
    'PC2解释方差比',
    False
)

# 第三主成分解释方差
var3_vis = {
    'min': 0,
    'max': float(explained_var_ratios[:, 2].max()),
    'palette': ['white', 'lightyellow', 'orange', 'red']
}

Map_results.addLayer(
    var_ratio_3.clip(geometry),
    var3_vis,
    'PC3解释方差比',
    False
)

# 累计解释方差
cumvar_vis = {
    'min': 0,
    'max': float(cumulative_var.max()),
    'palette': ['black', 'purple', 'blue', 'cyan', 'green', 'yellow', 'red']
}

Map_results.addLayer(
    cumulative_var_image.clip(geometry),
    cumvar_vis,
    '累计解释方差比',
    False
)

print("已添加解释方差可视化图层")
print("- PC1解释方差比")
print("- PC2解释方差比")
print("- PC3解释方差比")
print("- 累计解释方差比")

# 显示结果地图
print("\n=== 显示GWPCA分析结果 ===")

Map_results

# 生成统计报告
print("\n=== GWPCA分析统计报告 ===")
print("\n1. 数据概况:")
print(f"   - 研究区域：丝绸之路流域 (60°-120°E, 25°-50°N)")
print(f"   - 时间范围：2017-2024年（8年平均）")
print(f"   - 空间分辨率：0.25°")
print(f"   - 有效样本数：{len(embeddings_data)}")
print(f"   - 原始维度：64维")
print(f"   - 降维后维度：3维")

print("\n2. 主成分统计:")
print(f"   第一主成分 (PC1):")
print(f"     - 数值范围：{pc1_values.min():.4f} - {pc1_values.max():.4f}")
print(f"     - 平均解释方差比：{explained_var_ratios[:, 0].mean():.4f}")
print(f"     - 标准差：{pc1_values.std():.4f}")

print(f"   第二主成分 (PC2):")
print(f"     - 数值范围：{pc2_values.min():.4f} - {pc2_values.max():.4f}")
print(f"     - 平均解释方差比：{explained_var_ratios[:, 1].mean():.4f}")
print(f"     - 标准差：{pc2_values.std():.4f}")

print(f"   第三主成分 (PC3):")
print(f"     - 数值范围：{pc3_values.min():.4f} - {pc3_values.max():.4f}")
print(f"     - 平均解释方差比：{explained_var_ratios[:, 2].mean():.4f}")
print(f"     - 标准差：{pc3_values.std():.4f}")

print("\n3. 总体解释方差:")
total_explained = explained_var_ratios.sum(axis=1).mean()
print(f"   - 前三个主成分累计解释方差比：{total_explained:.4f}")
print(f"   - 信息保留率：{total_explained*100:.2f}%")

print("\n4. GWPCA参数:")
print(f"   - 空间权重函数：高斯核")
print(f"   - 带宽参数：{bandwidth}度")
print(f"   - 降维目标：3个主成分")

print("\n=== 分析完成 ===")
print("\n可视化图层说明:")
print("- 第一主成分 (PC1)：显示主要的空间变异模式")
print("- 第二主成分 (PC2)：显示次要的空间变异模式")
print("- 第三主成分 (PC3)：显示第三重要的空间变异模式")
print("- 解释方差比图层：显示各主成分在不同位置的重要性")
print("- 累计解释方差比：显示降维后信息保留的空间分布")