{"cells": [{"cell_type": "markdown", "id": "bbf8fb6c-d659-400b-ac45-c093d279c925", "metadata": {}, "source": ["2 编程实现\n", "\n", "2.1 导入必要包\n", "在本次教程中，我们需要使用三个常用的 python 数据分析工具包：numpy、pandas、matplotlib"]}, {"cell_type": "code", "execution_count": 1, "id": "7a1f896f-58ec-49bd-99b2-b35f9058cdf6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import ee\n", "import geemap\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "ee.Initialize()\n", "geemap.set_proxy(port=33210)"]}, {"cell_type": "markdown", "id": "305c7b30-e553-4a8f-92ab-a6df08f8b638", "metadata": {}, "source": ["2.2 加载数据\n", "1、首先，我们使用 geemap.shp_to_ee() 函数，直接调用本地的 shapefile 格式的长江流域数据。"]}, {"cell_type": "code", "execution_count": 2, "id": "5e4a9b71-9a59-4143-931e-66b36a2067cb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# yangtzeBasin = geemap.shp_to_ee('E:/researchData/Yangtze/YangtzeBasin.shp')\n", "path = r'E:\\Heritage\\FloodArea\\lyy\\1\\area1.shp'\n", "yangtzeBasin = geemap.shp_to_ee(path)"]}, {"cell_type": "markdown", "id": "945fcb98-eda1-4e77-a7e1-ba0f9c729515", "metadata": {}, "source": ["2、接着，我们设置研究的起止时间，这里小编亲测在 GEE Code Editor 平台中最多只能 9 年的时间跨度，超过就会出现用户内存报错，导致无法计算，这是 GEE 平台对用户的 算力限制。\n", "\n", "而使用 geemap 调用 GEE Python API 就不会轻易出现这个情况。这里我们设置研究时间为近 10 年：2013-01-01 ~ 2022-12-01，这在 Code Editor 平台上是无法直接实现的。"]}, {"cell_type": "code", "execution_count": 3, "id": "2b92a297-caf8-4cb4-bf76-224a85426ae2", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["startYear = 2013\n", "endYear = 2022\n", "# 为开始年份和结束年份创建两个日期对象\n", "startDate = ee.Date.fromYMD(startYear, 1, 1)\n", "endDate = ee.Date.fromYMD(endYear + 1, 1, 1)\n", "# 创建一个年份列表\n", "years = ee.List.sequence(startYear, endYear)\n", "# 创建一个月份列表\n", "months = ee.List.sequence(1, 12)"]}, {"cell_type": "markdown", "id": "26b7512b-5f57-4a18-968e-2f6e457fc2e9", "metadata": {}, "source": ["3、加载 CHIRPS 数据集"]}, {"cell_type": "code", "execution_count": 4, "id": "0a055c5a-3deb-46c7-ab8f-ab537c26d853", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["CHIRPS = ee.ImageCollection('UCSB-CHG/CHIRPS/PENTAD')\n", "CHIRPS = CHIRPS.filterDate(startDate, endDate)"]}, {"cell_type": "markdown", "id": "4056b118-ac7f-4399-9f49-c67533ea4d83", "metadata": {}, "source": ["2.3 计算每月总降雨量\n", "接下来，是降雨量计算的关键步骤，我们创建一个函数，该函数应用一个嵌套循环，首先在相关年份进行映射，然后在相关月份进行映射，返回值是一个包含月度降雨量(P)波段图像。\n", "\n", "最终将结果转化为一个只含有月度总降雨量信息的影像集合。"]}, {"cell_type": "code", "execution_count": 5, "id": "72f25ad2-faa7-48b1-bdf8-b17df1ab3e24", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def caculate_months(y):\n", "    def caculate_all(m):\n", "        # 筛选出每月降雨量并求出当月总和\n", "        P = CHIRPS.filter(ee.Filter.eq('year', y)) \\\n", "                .filter(ee.Filter.eq('month', m)) \\\n", "                .sum()\n", "        # 返回一个图像，只含有一个降雨量波段\n", "        return P.set('year', y) \\\n", "                .set('month', m) \\\n", "                .set('date', ee.Date.fromYMD(y, m, 1))\n", "    return months.map(caculate_all)\n", "# flatten() 将特征的特征集合转换为单个特征集合\n", "# 再将结果转化为 ImageCollection\n", "monthlyPrecip = ee.ImageCollection.fromImages(\n", "    years.map(caculate_months).flatten()\n", ")"]}, {"cell_type": "markdown", "id": "6dedfcc4-dcae-4cbc-955f-f3989ff71225", "metadata": {}, "source": ["3 结果可视化\n", "3.1 月均降水量数据地图可视化\n", "然后我们就可以将降雨量数据加载到地图中进行显示了。"]}, {"cell_type": "code", "execution_count": 6, "id": "8423d326-8c3c-4d2b-8827-f57c8001879b", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9fef59eea26b4846839c7427ae3bf462", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[31.988839110162058, 96.3253932879723], controls=(WidgetControl(options=['position', 'transparent_b…"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["precipVis = {\n", "  'min': 0,\n", "  'max': 250,\n", "  'palette': ['white', 'blue', 'darkblue', 'red', 'purple']\n", "};\n", "Map = geemap.Map()\n", "Map.centerObject(yangtzeBasin, 5)\n", "Map.addLayer(yangtzeBasin, {}, '长江流域')\n", "Map.addLayer(monthlyPrecip.mean().clip(yangtzeBasin), precipVis, '月均降水')\n", "Map"]}, {"cell_type": "markdown", "id": "57e2395f-a3f5-472d-b3a2-a27a2ecf857d", "metadata": {}, "source": ["3.2 将降雨量数据提取成 excel 表格\n", "1、我们定义一个函数，用于对每个影像的降雨量波段数值求均值，并返回一个包含降雨量和时间属性的Feature\n", "2、使用ee.ImageCollection.map()方法来对影像集合中每个影像应用上面定义的函数\n", "3、需要注意的是，map() 函数返回的是一个 ee.ComputedObject 类型的对象，所以我们还要将结果转化为 ee.FeatureCollection，才能利用 geemap 包转化成 DataFrame 格式\n", "4、将要素集合转化成 DataFrame 格式并导出到本地 excel 表格，可以看到近 10 年，120 个月的降雨量数据都导出在表格中了"]}, {"cell_type": "code", "execution_count": 14, "id": "c606bc60-a7a8-449b-b363-d9cceb0eb3e0", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["region = yangtzeBasin.geometry()\n", "def get_precip_mean_date(image):\n", "    # 对降雨量波段数值求均值，使用ee.Reducer.mean()方法，并指定区域和缩放比例\n", "    mean = image.reduceRegion(ee.Reducer.mean(), region, 5000).get('precipitation')\n", "    # 获取影像的日期，使用ee.Date()方法，并格式化为YYYY-MM-dd\n", "    date = ee.Date(image.get('date')).format('YYYY-MM-dd')\n", "    # 返回一个包含降雨量和时间的要素，使用ee.Feature()方法，并设置几何和属性\n", "    return ee.Feature(None, {'date': date, 'precip_mean': mean})\n", "precip_mean = ee.FeatureCollection(monthlyPrecip.map(get_precip_mean_date))\n", "# 将数据转化为 DataFrame 格式  ee_to_df (原来是geemap.ee_to_pandas()，现在已被替换)函数将 ee.Feature 格式数据转化为 DateFrame 格式数据\n", "df = geemap.ee_to_df(precip_mean)\n", "# 将时间数据从字符串格式重新转化为时间格式\n", "df['date'] = pd.to_datetime(df['date'], format='%Y-%m-%d')\n", "# 将 df 导出成 excel 表格\n", "outputFile = 'yangtze_precip_chart_monthly.xlsx'\n", "df.to_excel(outputFile, index=False)  # pip install openpyxl"]}, {"cell_type": "code", "execution_count": 15, "id": "107dff22-55f6-4347-9f87-d4d7b6a52920", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div><style>:root {\n", "  --font-color-primary: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --font-color-secondary: var(--jp-content-font-color2, rgba(0, 0, 0, 0.7));\n", "  --font-color-accent: rgba(123, 31, 162, 1);\n", "  --border-color: var(--jp-border-color2, #e0e0e0);\n", "  --background-color: var(--jp-layout-color0, white);\n", "  --background-color-row-even: var(--jp-layout-color1, white);\n", "  --background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --font-color-primary: rgba(255, 255, 255, 1);\n", "  --font-color-secondary: rgba(255, 255, 255, 0.7);\n", "  --font-color-accent: rgb(173, 132, 190);\n", "  --border-color: #2e2e2e;\n", "  --background-color: #111111;\n", "  --background-color-row-even: #111111;\n", "  --background-color-row-odd: #313131;\n", "}\n", "\n", ".eerepr {\n", "  padding: 1em;\n", "  line-height: 1.5em;\n", "  min-width: 300px;\n", "  max-width: 1200px;\n", "  overflow-y: scroll;\n", "  max-height: 600px;\n", "  border: 1px solid var(--border-color);\n", "  font-family: monospace;\n", "  font-size: 14px;\n", "}\n", "\n", ".eerepr li {\n", "  list-style-type: none;\n", "  margin: 0;\n", "}\n", "\n", ".eerepr ul {\n", "  padding-left: 1.5em !important;\n", "  margin: 0;\n", "}\n", "\n", ".eerepr > ul {\n", "  padding-left: 0 !important;\n", "}\n", "\n", ".eerepr summary {\n", "  color: var(--font-color-secondary);\n", "  cursor: pointer;\n", "  margin: 0;\n", "}\n", "\n", ".eerepr summary:hover {\n", "  color: var(--font-color-primary);\n", "  background-color: var(--background-color-row-odd)\n", "}\n", "\n", ".ee-k {\n", "  color: var(--font-color-accent);\n", "  margin-right: 6px;\n", "}\n", "\n", ".ee-v {\n", "  color: var(--font-color-primary);\n", "}\n", "\n", ".eerepr details > summary::before {\n", "  content: '▼';\n", "  display: inline-block;\n", "  margin-right: 6px;\n", "  transition: transform 0.2s;\n", "  transform: rotate(-90deg);\n", "}\n", "\n", ".eerepr details[open] > summary::before {\n", "  transform: rotate(0deg);\n", "}\n", "\n", ".eerepr details summary::-webkit-details-marker {\n", "  display:none;\n", "}\n", "\n", ".eerepr details summary {\n", "  list-style-type: none;\n", "}\n", "</style><div class='eerepr'><ul><li><details><summary>FeatureCollection (120 elements, 0 columns)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>FeatureCollection</span></li><li><details><summary>columns: Object (0 properties)</summary><ul></ul></details></li><li><details><summary>features: List (120 elements)</summary><ul><li><details><summary>0: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>0</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>1.7668216718650467</span></li></ul></details></li></ul></details></li><li><details><summary>1: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>1</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.8977483796461736</span></li></ul></details></li></ul></details></li><li><details><summary>2: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>2</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>9.519587702006302</span></li></ul></details></li></ul></details></li><li><details><summary>3: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>3</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>18.95029116775073</span></li></ul></details></li></ul></details></li><li><details><summary>4: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>4</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>47.1773122386745</span></li></ul></details></li></ul></details></li><li><details><summary>5: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>5</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>100.09507759004929</span></li></ul></details></li></ul></details></li><li><details><summary>6: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>6</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>152.20209783441692</span></li></ul></details></li></ul></details></li><li><details><summary>7: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>7</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>72.1072326078534</span></li></ul></details></li></ul></details></li><li><details><summary>8: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>8</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>72.49896873406273</span></li></ul></details></li></ul></details></li><li><details><summary>9: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>9</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>31.79548352423314</span></li></ul></details></li></ul></details></li><li><details><summary>10: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>10</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.315796478321732</span></li></ul></details></li></ul></details></li><li><details><summary>11: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>11</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2013-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.104002154837778</span></li></ul></details></li></ul></details></li><li><details><summary>12: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>12</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>1.8921769366229861</span></li></ul></details></li></ul></details></li><li><details><summary>13: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>13</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.6392461838533516</span></li></ul></details></li></ul></details></li><li><details><summary>14: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>14</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>8.889631273690428</span></li></ul></details></li></ul></details></li><li><details><summary>15: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>15</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>11.644680145454045</span></li></ul></details></li></ul></details></li><li><details><summary>16: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>16</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>27.96702629069508</span></li></ul></details></li></ul></details></li><li><details><summary>17: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>17</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>103.36300119343082</span></li></ul></details></li></ul></details></li><li><details><summary>18: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>18</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>151.4237654836676</span></li></ul></details></li></ul></details></li><li><details><summary>19: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>19</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>131.48565254807505</span></li></ul></details></li></ul></details></li><li><details><summary>20: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>20</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>94.84729608136776</span></li></ul></details></li></ul></details></li><li><details><summary>21: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>21</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>18.94635588836836</span></li></ul></details></li></ul></details></li><li><details><summary>22: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>22</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.5489632040373515</span></li></ul></details></li></ul></details></li><li><details><summary>23: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>23</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2014-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.03993545193201</span></li></ul></details></li></ul></details></li><li><details><summary>24: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>24</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.24678676540298</span></li></ul></details></li></ul></details></li><li><details><summary>25: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>25</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>4.2313073671774495</span></li></ul></details></li></ul></details></li><li><details><summary>26: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>26</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>7.0404813788817995</span></li></ul></details></li></ul></details></li><li><details><summary>27: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>27</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>18.399162729565866</span></li></ul></details></li></ul></details></li><li><details><summary>28: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>28</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>38.63231499057715</span></li></ul></details></li></ul></details></li><li><details><summary>29: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>29</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>84.45809605266723</span></li></ul></details></li></ul></details></li><li><details><summary>30: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>30</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>84.8679266335987</span></li></ul></details></li></ul></details></li><li><details><summary>31: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>31</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>118.25883147495387</span></li></ul></details></li></ul></details></li><li><details><summary>32: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>32</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>86.08279869501325</span></li></ul></details></li></ul></details></li><li><details><summary>33: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>33</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>31.703948934456452</span></li></ul></details></li></ul></details></li><li><details><summary>34: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>34</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.7551008856768973</span></li></ul></details></li></ul></details></li><li><details><summary>35: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>35</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2015-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.0702168496891296</span></li></ul></details></li></ul></details></li><li><details><summary>36: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>36</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.3363071720927326</span></li></ul></details></li></ul></details></li><li><details><summary>37: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>37</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.268248861715799</span></li></ul></details></li></ul></details></li><li><details><summary>38: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>38</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>8.62171822343578</span></li></ul></details></li></ul></details></li><li><details><summary>39: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>39</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>28.593256097009345</span></li></ul></details></li></ul></details></li><li><details><summary>40: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>40</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>50.55529329469722</span></li></ul></details></li></ul></details></li><li><details><summary>41: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>41</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>98.91757837550588</span></li></ul></details></li></ul></details></li><li><details><summary>42: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>42</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>159.17362679320826</span></li></ul></details></li></ul></details></li><li><details><summary>43: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>43</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>82.90018897758748</span></li></ul></details></li></ul></details></li><li><details><summary>44: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>44</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>95.02399786446529</span></li></ul></details></li></ul></details></li><li><details><summary>45: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>45</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>24.50017563105527</span></li></ul></details></li></ul></details></li><li><details><summary>46: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>46</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.364666348778391</span></li></ul></details></li></ul></details></li><li><details><summary>47: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>47</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2016-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.0815775450712204</span></li></ul></details></li></ul></details></li><li><details><summary>48: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>48</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.0767866322658413</span></li></ul></details></li></ul></details></li><li><details><summary>49: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>49</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>4.7957629271067495</span></li></ul></details></li></ul></details></li><li><details><summary>50: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>50</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>11.713958572683062</span></li></ul></details></li></ul></details></li><li><details><summary>51: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>51</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>20.879807167393515</span></li></ul></details></li></ul></details></li><li><details><summary>52: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>52</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>43.04211045228255</span></li></ul></details></li></ul></details></li><li><details><summary>53: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>53</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>93.19546768318996</span></li></ul></details></li></ul></details></li><li><details><summary>54: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>54</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>119.88575283765843</span></li></ul></details></li></ul></details></li><li><details><summary>55: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>55</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>129.26073177165537</span></li></ul></details></li></ul></details></li><li><details><summary>56: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>56</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>83.48602727475178</span></li></ul></details></li></ul></details></li><li><details><summary>57: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>57</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>27.821251081865693</span></li></ul></details></li></ul></details></li><li><details><summary>58: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>58</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.8332456682335425</span></li></ul></details></li></ul></details></li><li><details><summary>59: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>59</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2017-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>1.641903035694017</span></li></ul></details></li></ul></details></li><li><details><summary>60: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>60</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.3697958533778674</span></li></ul></details></li></ul></details></li><li><details><summary>61: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>61</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.896505656508136</span></li></ul></details></li></ul></details></li><li><details><summary>62: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>62</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>10.515884775977995</span></li></ul></details></li></ul></details></li><li><details><summary>63: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>63</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>15.221908320680221</span></li></ul></details></li></ul></details></li><li><details><summary>64: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>64</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>42.031753906564056</span></li></ul></details></li></ul></details></li><li><details><summary>65: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>65</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>120.79665350432515</span></li></ul></details></li></ul></details></li><li><details><summary>66: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>66</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>147.4096973296065</span></li></ul></details></li></ul></details></li><li><details><summary>67: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>67</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>114.71605554089122</span></li></ul></details></li></ul></details></li><li><details><summary>68: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>68</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>104.3190838440616</span></li></ul></details></li></ul></details></li><li><details><summary>69: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>69</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>25.51173944167661</span></li></ul></details></li></ul></details></li><li><details><summary>70: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>70</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.0434249142831633</span></li></ul></details></li></ul></details></li><li><details><summary>71: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>71</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2018-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.0673847427791388</span></li></ul></details></li></ul></details></li><li><details><summary>72: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>72</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.9713004614269245</span></li></ul></details></li></ul></details></li><li><details><summary>73: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>73</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.121469954944831</span></li></ul></details></li></ul></details></li><li><details><summary>74: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>74</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>8.594844699726387</span></li></ul></details></li></ul></details></li><li><details><summary>75: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>75</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>16.00843449881409</span></li></ul></details></li></ul></details></li><li><details><summary>76: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>76</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>35.22803430630237</span></li></ul></details></li></ul></details></li><li><details><summary>77: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>77</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>81.88187330454606</span></li></ul></details></li></ul></details></li><li><details><summary>78: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>78</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>131.0911518997122</span></li></ul></details></li></ul></details></li><li><details><summary>79: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>79</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>113.00504877741167</span></li></ul></details></li></ul></details></li><li><details><summary>80: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>80</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>101.27464776468905</span></li></ul></details></li></ul></details></li><li><details><summary>81: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>81</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>30.14208196705015</span></li></ul></details></li></ul></details></li><li><details><summary>82: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>82</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.2210395016131512</span></li></ul></details></li></ul></details></li><li><details><summary>83: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>83</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2019-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.72688437261838</span></li></ul></details></li></ul></details></li><li><details><summary>84: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>84</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.965267101193169</span></li></ul></details></li></ul></details></li><li><details><summary>85: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>85</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>6.985210938408496</span></li></ul></details></li></ul></details></li><li><details><summary>86: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>86</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.953710940810607</span></li></ul></details></li></ul></details></li><li><details><summary>87: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>87</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>22.038851554839475</span></li></ul></details></li></ul></details></li><li><details><summary>88: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>88</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>37.9409252770886</span></li></ul></details></li></ul></details></li><li><details><summary>89: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>89</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>79.99852765893107</span></li></ul></details></li></ul></details></li><li><details><summary>90: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>90</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>145.76694217781971</span></li></ul></details></li></ul></details></li><li><details><summary>91: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>91</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>108.56911312353657</span></li></ul></details></li></ul></details></li><li><details><summary>92: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>92</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>89.3682675522542</span></li></ul></details></li></ul></details></li><li><details><summary>93: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>93</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>22.29696604901048</span></li></ul></details></li></ul></details></li><li><details><summary>94: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>94</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.7571706119256207</span></li></ul></details></li></ul></details></li><li><details><summary>95: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>95</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2020-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>1.7656088949406124</span></li></ul></details></li></ul></details></li><li><details><summary>96: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>96</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>1.9717056577826855</span></li></ul></details></li></ul></details></li><li><details><summary>97: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>97</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>3.3947686173126796</span></li></ul></details></li></ul></details></li><li><details><summary>98: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>98</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.486524132477512</span></li></ul></details></li></ul></details></li><li><details><summary>99: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>99</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>10.738465512613459</span></li></ul></details></li></ul></details></li><li><details><summary>100: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>100</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>42.17647073611545</span></li></ul></details></li></ul></details></li><li><details><summary>101: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>101</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>109.86480381879267</span></li></ul></details></li></ul></details></li><li><details><summary>102: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>102</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>111.0703836992019</span></li></ul></details></li></ul></details></li><li><details><summary>103: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>103</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>120.33366261937282</span></li></ul></details></li></ul></details></li><li><details><summary>104: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>104</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>68.26254288500817</span></li></ul></details></li></ul></details></li><li><details><summary>105: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>105</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>25.305103311457948</span></li></ul></details></li></ul></details></li><li><details><summary>106: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>106</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.6399359911018094</span></li></ul></details></li></ul></details></li><li><details><summary>107: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>107</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2021-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.1238705225578585</span></li></ul></details></li></ul></details></li><li><details><summary>108: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>108</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-01-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.453727704035243</span></li></ul></details></li></ul></details></li><li><details><summary>109: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>109</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-02-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.655754858442246</span></li></ul></details></li></ul></details></li><li><details><summary>110: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>110</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-03-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>5.949708715053763</span></li></ul></details></li></ul></details></li><li><details><summary>111: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>111</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-04-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>25.994096611699423</span></li></ul></details></li></ul></details></li><li><details><summary>112: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>112</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-05-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>42.70460864142084</span></li></ul></details></li></ul></details></li><li><details><summary>113: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>113</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-06-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>112.88468000409017</span></li></ul></details></li></ul></details></li><li><details><summary>114: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>114</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-07-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>93.46408870694215</span></li></ul></details></li></ul></details></li><li><details><summary>115: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>115</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-08-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>88.9754546766371</span></li></ul></details></li></ul></details></li><li><details><summary>116: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>116</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-09-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>86.50898554836049</span></li></ul></details></li></ul></details></li><li><details><summary>117: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>117</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-10-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>27.142492536285253</span></li></ul></details></li></ul></details></li><li><details><summary>118: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>118</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-11-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.2492512443802855</span></li></ul></details></li></ul></details></li><li><details><summary>119: Feature (2 properties)</summary><ul><li><span class='ee-k'>type:</span><span class='ee-v'>Feature</span></li><li><span class='ee-k'>id:</span><span class='ee-v'>119</span></li><li><span class='ee-k'>geometry:</span><span class='ee-v'>None</span></li><li><details><summary>properties: Object (2 properties)</summary><ul><li><span class='ee-k'>date:</span><span class='ee-v'>2022-12-01</span></li><li><span class='ee-k'>precip_mean:</span><span class='ee-v'>2.0791343977721937</span></li></ul></details></li></ul></details></li></ul></details></li></ul></details></li></ul></div></div>"], "text/plain": ["<ee.featurecollection.FeatureCollection at 0x193694354f0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["precip_mean"]}, {"cell_type": "code", "execution_count": 12, "id": "2bbd57f7-eec2-4441-a34e-b050c3471400", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 将 df 导出成 excel 表格\n", "outputFile2 = 'yangtze_precip_chart_monthly.csv'\n", "df.to_csv(outputFile2, index=False)"]}, {"cell_type": "markdown", "id": "255828e7-c60e-4732-a8da-832999555f2a", "metadata": {}, "source": ["3.3 绘制降雨量直方图\n", "最后，我们利用 matplotlib 包，实现长江流域近十年降雨量直方图的绘制。"]}, {"cell_type": "code", "execution_count": 13, "id": "c26911d7-a43e-4d84-a0a2-97d36f6ab8ec", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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**************************************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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df = pd.read_excel(outputFile)\n", "# 设置图形的大小\n", "plt.figure(figsize=(12, 6))\n", "# 将date读取为只显示年份-月份的形式\n", "df['date'] = df['date'].dt.strftime('%Y-%m')\n", "# 绘制柱状图，使用日期作为x轴，平均降水量作为y轴\n", "plt.bar(df['date'], df['precip_mean'])\n", "# 设置x轴的刻度，每隔1年(12个日期)显示一个标签\n", "plt.xticks(np.arange(0, len(df), 12))\n", "# 设置x轴和y轴的标签\n", "plt.xlabel('Time')\n", "plt.ylabel('Precipitation (mm)')\n", "# 设置图形的标题\n", "plt.title('Yangtze River Basin monthly precipitation (2013~2022)')\n", "# 显示图形\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}