@echo off
echo ========================================
echo Satellite Embedding 数据下载工具
echo ========================================
echo.

echo 请选择下载方式:
echo 1. 测试下载 (推荐首次使用)
echo 2. 完整下载 (Jupyter Notebook)
echo 3. 批处理下载 (命令行)
echo 4. 查看使用说明
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 启动测试下载...
    jupyter notebook test_download_satellite_embedding.ipynb
) else if "%choice%"=="2" (
    echo.
    echo 启动完整下载...
    jupyter notebook download_satellite_embedding_025.ipynb
) else if "%choice%"=="3" (
    echo.
    echo 启动批处理下载...
    python batch_download_satellite_embedding.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo 打开使用说明...
    start README_download.md
) else (
    echo.
    echo 无效选择，请重新运行脚本
    pause
)

echo.
echo 按任意键退出...
pause >nul
