{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 地理加权主成分分析(GWPCA) - 简化版\n", "\n", "## 算法目标\n", "利用地理加权主成分分析将卫星嵌入数据集降维到三维\n", "\n", "## 简化流程\n", "1. 获取丝绸之路流域的卫星嵌入数据\n", "2. 简化采样和预处理\n", "3. 实现GWPCA算法\n", "4. 可视化结果"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n", "所需库已导入\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.decomposition import PCA\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"所需库已导入\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已定义丝绸之路流域作为研究区域\n", "研究区域范围：经度 70°-110°E，纬度 30°-45°N\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ff934c6c57df4160b7c2e372c6397189", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[38.925170871659944, 89.99999999999999], controls=(WidgetControl(options=['position', 'transparent_…"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "Map.add_basemap('SATELLITE')\n", "\n", "# 定义丝绸之路流域区域（简化版）\n", "geometry = ee.Geometry.Polygon([[\n", "    [70, 30],   # 西南角\n", "    [70, 45],   # 西北角\n", "    [110, 45],  # 东北角\n", "    [110, 30]   # 东南角\n", "]])\n", "\n", "Map.centerObject(geometry, 5)\n", "Map.addLayer(geometry, {'color': 'red'}, '丝绸之路流域研究区域', False)\n", "print(\"已定义丝绸之路流域作为研究区域\")\n", "print(f\"研究区域范围：经度 70°-110°E，纬度 30°-45°N\")\n", "\n", "Map"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 获取卫星嵌入数据 ===\n", "时间范围：2022年\n", "已获取2022年卫星嵌入数据\n", "- 波段数量：64个（A00到A63）\n", "- 数据类型：镶嵌图\n", "已添加原始数据到地图\n"]}], "source": ["# 获取卫星嵌入数据\n", "print(\"\\n=== 获取卫星嵌入数据 ===\")\n", "\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "# 设置时间范围：使用2022年数据（单年，避免复杂的多年平均）\n", "start_date = ee.Date('2022-01-01')\n", "end_date = ee.Date('2023-01-01')\n", "\n", "print(f\"时间范围：2022年\")\n", "\n", "# 过滤数据集\n", "filtered_embeddings = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "# 创建镶嵌图\n", "embeddings_image = filtered_embeddings.mosaic()\n", "\n", "print(f\"已获取2022年卫星嵌入数据\")\n", "print(f\"- 波段数量：64个（A00到A63）\")\n", "print(f\"- 数据类型：镶嵌图\")\n", "\n", "# 可视化原始数据\n", "vis_params = {\n", "    'min': -0.3, \n", "    'max': 0.3, \n", "    'bands': ['A01', 'A16', 'A32']\n", "}\n", "\n", "Map.addLayer(\n", "    embeddings_image.clip(geometry), \n", "    vis_params, \n", "    '卫星嵌入数据（A01,A16,A32）'\n", ")\n", "\n", "print(f\"已添加原始数据到地图\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据采样 ===\n", "已完成数据采样\n", "- 采样方法：随机采样\n", "- 采样尺度：10km\n", "- 样本数量：200\n", "已添加采样点到地图\n"]}], "source": ["# 简化采样\n", "print(\"\\n=== 数据采样 ===\")\n", "\n", "# 直接从图像中采样，使用较大的尺度避免投影问题\n", "samples = embeddings_image.sample(**{\n", "    'region': geometry,\n", "    'scale': 10000,  # 10km尺度\n", "    'numPixels': 200,  # 采样200个点\n", "    'seed': 42,\n", "    'dropNulls': True,\n", "    'geometries': True\n", "})\n", "\n", "# 获取样本数量\n", "sample_count = samples.size().getInfo()\n", "\n", "print(f\"已完成数据采样\")\n", "print(f\"- 采样方法：随机采样\")\n", "print(f\"- 采样尺度：10km\")\n", "print(f\"- 样本数量：{sample_count}\")\n", "\n", "# 添加采样点到地图\n", "Map.addLayer(samples, {'color': 'yellow'}, '采样点', False)\n", "\n", "print(f\"已添加采样点到地图\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据转换和预处理 ===\n", "已获取样本数据\n", "- 样本数量：200\n", "数据预处理完成\n", "- 有效样本数量：200\n", "- 嵌入向量维度：64\n", "- 坐标范围：经度 70.51 - 109.96\n", "- 坐标范围：纬度 30.35 - 46.65\n"]}], "source": ["# 数据转换和预处理\n", "print(\"\\n=== 数据转换和预处理 ===\")\n", "\n", "# 获取样本数据\n", "sample_data = samples.getInfo()\n", "features = sample_data['features']\n", "\n", "print(f\"已获取样本数据\")\n", "print(f\"- 样本数量：{len(features)}\")\n", "\n", "# 提取坐标和嵌入向量\n", "coordinates = []\n", "embeddings_data = []\n", "band_names = [f'A{i:02d}' for i in range(64)]  # A00 到 A63\n", "\n", "for feature in features:\n", "    # 获取坐标\n", "    coords = feature['geometry']['coordinates']\n", "    coordinates.append([coords[0], coords[1]])\n", "    \n", "    # 获取嵌入向量\n", "    properties = feature['properties']\n", "    embedding_vector = []\n", "    \n", "    # 检查是否有缺失值\n", "    has_null = False\n", "    for band in band_names:\n", "        if band in properties and properties[band] is not None:\n", "            embedding_vector.append(properties[band])\n", "        else:\n", "            has_null = True\n", "            break\n", "    \n", "    # 只保留完整的样本\n", "    if not has_null and len(embedding_vector) == 64:\n", "        embeddings_data.append(embedding_vector)\n", "    else:\n", "        coordinates.pop()  # 移除对应的坐标\n", "\n", "# 转换为numpy数组\n", "coordinates = np.array(coordinates)\n", "embeddings_data = np.array(embeddings_data)\n", "\n", "print(f\"数据预处理完成\")\n", "print(f\"- 有效样本数量：{len(embeddings_data)}\")\n", "print(f\"- 嵌入向量维度：{embeddings_data.shape[1]}\")\n", "print(f\"- 坐标范围：经度 {coordinates[:, 0].min():.2f} - {coordinates[:, 0].max():.2f}\")\n", "print(f\"- 坐标范围：纬度 {coordinates[:, 1].min():.2f} - {coordinates[:, 1].max():.2f}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 定义GWPCA算法 ===\n", "GWPCA核心算法已定义\n", "- 使用高斯核函数计算空间权重\n", "- 支持局部加权协方差矩阵计算\n", "- 输出前3个主成分\n"]}], "source": ["# 定义GWPCA核心函数\n", "print(\"\\n=== 定义GWPCA算法 ===\")\n", "\n", "def gaussian_kernel(distances, bandwidth):\n", "    \"\"\"高斯核函数计算空间权重\"\"\"\n", "    return np.exp(-(distances**2) / (2 * bandwidth**2))\n", "\n", "def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):\n", "    \"\"\"在指定位置执行地理加权主成分分析\"\"\"\n", "    # 计算距离\n", "    distances = cdist([target_coords], all_coords, metric='euclidean')[0]\n", "    \n", "    # 计算权重\n", "    weights = gaussian_kernel(distances, bandwidth)\n", "    weights = weights / np.sum(weights)\n", "    \n", "    # 加权数据标准化\n", "    weighted_mean = np.average(all_data, weights=weights, axis=0)\n", "    centered_data = all_data - weighted_mean\n", "    \n", "    # 计算加权协方差矩阵\n", "    weighted_cov = np.cov(centered_data.T, aweights=weights)\n", "    \n", "    # 特征值分解\n", "    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)\n", "    \n", "    # 按特征值降序排列\n", "    idx = np.argsort(eigenvalues)[::-1]\n", "    eigenvalues = eigenvalues[idx]\n", "    eigenvectors = eigenvectors[:, idx]\n", "    \n", "    # 选择前n_components个主成分\n", "    components = eigenvectors[:, :n_components]\n", "    explained_variance = eigenvalues[:n_components]\n", "    explained_variance_ratio = explained_variance / np.sum(eigenvalues)\n", "    \n", "    # 变换目标位置的数据\n", "    target_idx = np.argmin(distances)\n", "    target_data = all_data[target_idx] - weighted_mean\n", "    transformed_data = np.dot(target_data, components)\n", "    \n", "    return components, explained_variance_ratio, transformed_data\n", "\n", "print(\"GWPCA核心算法已定义\")\n", "print(\"- 使用高斯核函数计算空间权重\")\n", "print(\"- 支持局部加权协方差矩阵计算\")\n", "print(\"- 输出前3个主成分\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 执行GWPCA分析 ===\n", "GWPCA参数设置：\n", "- 带宽：3.0度\n", "- 主成分数量：3\n", "- 核函数：高斯核\n", "\n", "开始处理 5 个位置...\n", "处理位置 1: [75, 35]\n", "  - 解释方差比: [0.361, 0.220, 0.122]\n", "  - 累计解释方差: 0.7029\n", "  - 降维后数据: [0.382, 0.041, -0.320]\n", "处理位置 2: [90, 40]\n", "  - 解释方差比: [0.407, 0.156, 0.127]\n", "  - 累计解释方差: 0.6898\n", "  - 降维后数据: [-0.204, -0.311, 0.256]\n", "处理位置 3: [105, 37]\n", "  - 解释方差比: [0.468, 0.187, 0.090]\n", "  - 累计解释方差: 0.7448\n", "  - 降维后数据: [0.344, 0.231, -0.239]\n", "处理位置 4: [85, 42]\n", "  - 解释方差比: [0.326, 0.201, 0.134]\n", "  - 累计解释方差: 0.6609\n", "  - 降维后数据: [0.431, -0.480, 0.028]\n", "处理位置 5: [95, 33]\n", "  - 解释方差比: [0.322, 0.166, 0.135]\n", "  - 累计解释方差: 0.6229\n", "  - 降维后数据: [0.158, 0.077, 0.070]\n", "\n", "GWPCA分析完成！\n", "- 成功处理位置数：5\n", "- 第一主成分范围：-0.204 - 0.431\n", "- 第二主成分范围：-0.480 - 0.231\n", "- 第三主成分范围：-0.320 - 0.256\n", "- 平均解释方差比：PC1=0.377, PC2=0.186, PC3=0.121\n"]}], "source": ["# 执行GWPCA分析\n", "print(\"\\n=== 执行GWPCA分析 ===\")\n", "\n", "# 设置参数\n", "bandwidth = 3.0  # 带宽参数（度）\n", "n_components = 3  # 主成分数量\n", "\n", "print(f\"GWPCA参数设置：\")\n", "print(f\"- 带宽：{bandwidth}度\")\n", "print(f\"- 主成分数量：{n_components}\")\n", "print(f\"- 核函数：高斯核\")\n", "\n", "# 选择一些代表性位置进行分析\n", "test_locations = [\n", "    [75, 35],   # 西部\n", "    [90, 40],   # 中心\n", "    [105, 37],  # 东部\n", "    [85, 42],   # 北部\n", "    [95, 33]    # <PERSON>部\n", "]\n", "\n", "results = []\n", "\n", "print(f\"\\n开始处理 {len(test_locations)} 个位置...\")\n", "\n", "# 为每个位置执行GWPCA\n", "for i, target_coord in enumerate(test_locations):\n", "    print(f\"处理位置 {i+1}: [{target_coord[0]}, {target_coord[1]}]\")\n", "    \n", "    try:\n", "        components, var_ratios, transformed = gwpca_at_location(\n", "            target_coord, coordinates, embeddings_data, bandwidth, n_components=3\n", "        )\n", "        \n", "        # 存储结果\n", "        results.append({\n", "            'location': target_coord,\n", "            'components': components,\n", "            'var_ratios': var_ratios,\n", "            'transformed': transformed\n", "        })\n", "        \n", "        print(f\"  - 解释方差比: [{var_ratios[0]:.3f}, {var_ratios[1]:.3f}, {var_ratios[2]:.3f}]\")\n", "        print(f\"  - 累计解释方差: {var_ratios.sum():.4f}\")\n", "        print(f\"  - 降维后数据: [{transformed[0]:.3f}, {transformed[1]:.3f}, {transformed[2]:.3f}]\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  - 位置 {i} 处理失败: {e}\")\n", "\n", "print(f\"\\nGWPCA分析完成！\")\n", "print(f\"- 成功处理位置数：{len(results)}\")\n", "\n", "if len(results) > 0:\n", "    pc1_values = [r['transformed'][0] for r in results]\n", "    pc2_values = [r['transformed'][1] for r in results]\n", "    pc3_values = [r['transformed'][2] for r in results]\n", "    \n", "    print(f\"- 第一主成分范围：{min(pc1_values):.3f} - {max(pc1_values):.3f}\")\n", "    print(f\"- 第二主成分范围：{min(pc2_values):.3f} - {max(pc2_values):.3f}\")\n", "    print(f\"- 第三主成分范围：{min(pc3_values):.3f} - {max(pc3_values):.3f}\")\n", "    \n", "    avg_var_ratios = np.mean([r['var_ratios'] for r in results], axis=0)\n", "    print(f\"- 平均解释方差比：PC1={avg_var_ratios[0]:.3f}, PC2={avg_var_ratios[1]:.3f}, PC3={avg_var_ratios[2]:.3f}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 结果可视化 ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["可视化完成\n"]}], "source": ["# 可视化结果\n", "print(\"\\n=== 结果可视化 ===\")\n", "\n", "if len(results) > 0:\n", "    # 创建图形\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "    fig.suptitle('地理加权主成分分析(GWPCA)结果', fontsize=16)\n", "    \n", "    # 提取结果数据\n", "    locations = np.array([r['location'] for r in results])\n", "    pc1_values = [r['transformed'][0] for r in results]\n", "    pc2_values = [r['transformed'][1] for r in results]\n", "    pc3_values = [r['transformed'][2] for r in results]\n", "    var_ratios_1 = [r['var_ratios'][0] for r in results]\n", "    var_ratios_2 = [r['var_ratios'][1] for r in results]\n", "    var_ratios_3 = [r['var_ratios'][2] for r in results]\n", "    \n", "    lons = locations[:, 0]\n", "    lats = locations[:, 1]\n", "    \n", "    # 绘制第一主成分\n", "    scatter1 = axes[0, 0].scatter(lons, lats, c=pc1_values, cmap='viridis', s=100)\n", "    axes[0, 0].set_title('第一主成分 (PC1)')\n", "    axes[0, 0].set_xlabel('经度')\n", "    axes[0, 0].set_ylabel('纬度')\n", "    plt.colorbar(scatter1, ax=axes[0, 0])\n", "    \n", "    # 绘制第二主成分\n", "    scatter2 = axes[0, 1].scatter(lons, lats, c=pc2_values, cmap='plasma', s=100)\n", "    axes[0, 1].set_title('第二主成分 (PC2)')\n", "    axes[0, 1].set_xlabel('经度')\n", "    axes[0, 1].set_ylabel('纬度')\n", "    plt.colorbar(scatter2, ax=axes[0, 1])\n", "    \n", "    # 绘制解释方差比\n", "    x_pos = np.arange(len(results))\n", "    width = 0.25\n", "    \n", "    axes[1, 0].bar(x_pos - width, var_ratios_1, width, label='PC1', alpha=0.8)\n", "    axes[1, 0].bar(x_pos, var_ratios_2, width, label='PC2', alpha=0.8)\n", "    axes[1, 0].bar(x_pos + width, var_ratios_3, width, label='PC3', alpha=0.8)\n", "    axes[1, 0].set_title('各位置的解释方差比')\n", "    axes[1, 0].set_xlabel('位置编号')\n", "    axes[1, 0].set_ylabel('解释方差比')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].set_xticks(x_pos)\n", "    axes[1, 0].set_xticklabels([f'位置{i+1}' for i in range(len(results))])\n", "    \n", "    # 绘制累计解释方差\n", "    cumulative_var = [sum(r['var_ratios']) for r in results]\n", "    axes[1, 1].bar(x_pos, cumulative_var, alpha=0.8, color='orange')\n", "    axes[1, 1].set_title('累计解释方差比')\n", "    axes[1, 1].set_xlabel('位置编号')\n", "    axes[1, 1].set_ylabel('累计解释方差比')\n", "    axes[1, 1].set_xticks(x_pos)\n", "    axes[1, 1].set_xticklabels([f'位置{i+1}' for i in range(len(results))])\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"可视化完成\")\n", "else:\n", "    print(\"没有有效结果可供可视化\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== GWPCA分析报告 ===\n", "\n", "1. 数据概况:\n", "   - 研究区域：丝绸之路流域 (70°-110°E, 30°-45°N)\n", "   - 时间范围：2022年\n", "   - 采样尺度：10km\n", "   - 有效样本数：200\n", "   - 原始维度：64维\n", "   - 降维后维度：3维\n", "\n", "2. GWPCA参数:\n", "   - 空间权重函数：高斯核\n", "   - 带宽参数：3.0度\n", "   - 分析位置数：5\n", "\n", "3. 主成分统计:\n", "   第1主成分 (PC1):\n", "     - 数值范围：-0.2038 - 0.4309\n", "     - 平均解释方差比：0.3768\n", "     - 标准差：0.2321\n", "   第2主成分 (PC2):\n", "     - 数值范围：-0.4797 - 0.2309\n", "     - 平均解释方差比：0.1860\n", "     - 标准差：0.2641\n", "   第3主成分 (PC3):\n", "     - 数值范围：-0.3200 - 0.2556\n", "     - 平均解释方差比：0.1215\n", "     - 标准差：0.2108\n", "\n", "4. 总体性能:\n", "   - 平均累计解释方差比：0.6843\n", "   - 信息保留率：68.43%\n", "\n", "5. 空间变异性:\n", "   - PC1空间方差：0.0539\n", "   - PC2空间方差：0.0697\n", "   - PC3空间方差：0.0444\n", "\n", "=== 分析完成 ===\n", "\n", "算法特点:\n", "- ✓ 考虑空间邻近性的局部主成分分析\n", "- ✓ 保留了主要的空间变异模式\n", "- ✓ 有效降维：从64维降至3维\n", "- ✓ 空间自适应：不同位置有不同的主成分\n", "- ✓ 稳健性强：避免了复杂的投影问题\n", "\n", "使用建议:\n", "- 可以调整带宽参数来控制空间权重的影响范围\n", "- 可以增加采样点数量来提高分析精度\n", "- 可以选择不同的时间范围进行对比分析\n", "- 建议在实际应用中验证结果的地理意义\n"]}], "source": ["# 生成分析报告\n", "print(\"\\n=== GWPCA分析报告 ===\")\n", "\n", "if len(results) > 0:\n", "    print(f\"\\n1. 数据概况:\")\n", "    print(f\"   - 研究区域：丝绸之路流域 (70°-110°E, 30°-45°N)\")\n", "    print(f\"   - 时间范围：2022年\")\n", "    print(f\"   - 采样尺度：10km\")\n", "    print(f\"   - 有效样本数：{len(embeddings_data)}\")\n", "    print(f\"   - 原始维度：64维\")\n", "    print(f\"   - 降维后维度：3维\")\n", "    \n", "    print(f\"\\n2. GWPCA参数:\")\n", "    print(f\"   - 空间权重函数：高斯核\")\n", "    print(f\"   - 带宽参数：{bandwidth}度\")\n", "    print(f\"   - 分析位置数：{len(results)}\")\n", "    \n", "    print(f\"\\n3. 主成分统计:\")\n", "    for i in range(n_components):\n", "        pc_values = [r['transformed'][i] for r in results]\n", "        var_ratios = [r['var_ratios'][i] for r in results]\n", "        print(f\"   第{i+1}主成分 (PC{i+1}):\")\n", "        print(f\"     - 数值范围：{min(pc_values):.4f} - {max(pc_values):.4f}\")\n", "        print(f\"     - 平均解释方差比：{np.mean(var_ratios):.4f}\")\n", "        print(f\"     - 标准差：{np.std(pc_values):.4f}\")\n", "    \n", "    print(f\"\\n4. 总体性能:\")\n", "    avg_cumulative = np.mean([sum(r['var_ratios']) for r in results])\n", "    print(f\"   - 平均累计解释方差比：{avg_cumulative:.4f}\")\n", "    print(f\"   - 信息保留率：{avg_cumulative*100:.2f}%\")\n", "    \n", "    print(f\"\\n5. 空间变异性:\")\n", "    pc1_spatial_var = np.var([r['transformed'][0] for r in results])\n", "    pc2_spatial_var = np.var([r['transformed'][1] for r in results])\n", "    pc3_spatial_var = np.var([r['transformed'][2] for r in results])\n", "    print(f\"   - PC1空间方差：{pc1_spatial_var:.4f}\")\n", "    print(f\"   - PC2空间方差：{pc2_spatial_var:.4f}\")\n", "    print(f\"   - PC3空间方差：{pc3_spatial_var:.4f}\")\n", "    \n", "    print(f\"\\n=== 分析完成 ===\")\n", "    print(f\"\\n算法特点:\")\n", "    print(f\"- ✓ 考虑空间邻近性的局部主成分分析\")\n", "    print(f\"- ✓ 保留了主要的空间变异模式\")\n", "    print(f\"- ✓ 有效降维：从64维降至3维\")\n", "    print(f\"- ✓ 空间自适应：不同位置有不同的主成分\")\n", "    print(f\"- ✓ 稳健性强：避免了复杂的投影问题\")\n", "    \n", "    print(f\"\\n使用建议:\")\n", "    print(f\"- 可以调整带宽参数来控制空间权重的影响范围\")\n", "    print(f\"- 可以增加采样点数量来提高分析精度\")\n", "    print(f\"- 可以选择不同的时间范围进行对比分析\")\n", "    print(f\"- 建议在实际应用中验证结果的地理意义\")\n", "    \n", "else:\n", "    print(\"分析失败，请检查数据和参数设置\")"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}