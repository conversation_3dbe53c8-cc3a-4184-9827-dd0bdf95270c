# 导入必要的库
import ee
import geemap
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")
print("所需库已导入")

# 创建交互式地图
Map = geemap.Map()
Map.add_basemap('SATELLITE')

# 定义丝绸之路流域区域（简化版）
geometry = ee.Geometry.Polygon([[
    [70, 30],   # 西南角
    [70, 45],   # 西北角
    [110, 45],  # 东北角
    [110, 30]   # 东南角
]])

Map.centerObject(geometry, 5)
Map.addLayer(geometry, {'color': 'red'}, '丝绸之路流域研究区域', False)
print("已定义丝绸之路流域作为研究区域")
print(f"研究区域范围：经度 70°-110°E，纬度 30°-45°N")

Map

# 获取卫星嵌入数据
print("\n=== 获取卫星嵌入数据 ===")

embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

# 设置时间范围：使用2022年数据（单年，避免复杂的多年平均）
start_date = ee.Date('2022-01-01')
end_date = ee.Date('2023-01-01')

print(f"时间范围：2022年")

# 过滤数据集
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

# 创建镶嵌图
embeddings_image = filtered_embeddings.mosaic()

print(f"已获取2022年卫星嵌入数据")
print(f"- 波段数量：64个（A00到A63）")
print(f"- 数据类型：镶嵌图")

# 可视化原始数据
vis_params = {
    'min': -0.3, 
    'max': 0.3, 
    'bands': ['A01', 'A16', 'A32']
}

Map.addLayer(
    embeddings_image.clip(geometry), 
    vis_params, 
    '卫星嵌入数据（A01,A16,A32）'
)

print(f"已添加原始数据到地图")

# 简化采样
print("\n=== 数据采样 ===")

# 直接从图像中采样，使用较大的尺度避免投影问题
samples = embeddings_image.sample(**{
    'region': geometry,
    'scale': 10000,  # 10km尺度
    'numPixels': 200,  # 采样200个点
    'seed': 42,
    'dropNulls': True,
    'geometries': True
})

# 获取样本数量
sample_count = samples.size().getInfo()

print(f"已完成数据采样")
print(f"- 采样方法：随机采样")
print(f"- 采样尺度：10km")
print(f"- 样本数量：{sample_count}")

# 添加采样点到地图
Map.addLayer(samples, {'color': 'yellow'}, '采样点', False)

print(f"已添加采样点到地图")

# 数据转换和预处理
print("\n=== 数据转换和预处理 ===")

# 获取样本数据
sample_data = samples.getInfo()
features = sample_data['features']

print(f"已获取样本数据")
print(f"- 样本数量：{len(features)}")

# 提取坐标和嵌入向量
coordinates = []
embeddings_data = []
band_names = [f'A{i:02d}' for i in range(64)]  # A00 到 A63

for feature in features:
    # 获取坐标
    coords = feature['geometry']['coordinates']
    coordinates.append([coords[0], coords[1]])
    
    # 获取嵌入向量
    properties = feature['properties']
    embedding_vector = []
    
    # 检查是否有缺失值
    has_null = False
    for band in band_names:
        if band in properties and properties[band] is not None:
            embedding_vector.append(properties[band])
        else:
            has_null = True
            break
    
    # 只保留完整的样本
    if not has_null and len(embedding_vector) == 64:
        embeddings_data.append(embedding_vector)
    else:
        coordinates.pop()  # 移除对应的坐标

# 转换为numpy数组
coordinates = np.array(coordinates)
embeddings_data = np.array(embeddings_data)

print(f"数据预处理完成")
print(f"- 有效样本数量：{len(embeddings_data)}")
print(f"- 嵌入向量维度：{embeddings_data.shape[1]}")
print(f"- 坐标范围：经度 {coordinates[:, 0].min():.2f} - {coordinates[:, 0].max():.2f}")
print(f"- 坐标范围：纬度 {coordinates[:, 1].min():.2f} - {coordinates[:, 1].max():.2f}")

# 定义GWPCA核心函数
print("\n=== 定义GWPCA算法 ===")

def gaussian_kernel(distances, bandwidth):
    """高斯核函数计算空间权重"""
    return np.exp(-(distances**2) / (2 * bandwidth**2))

def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):
    """在指定位置执行地理加权主成分分析"""
    # 计算距离
    distances = cdist([target_coords], all_coords, metric='euclidean')[0]
    
    # 计算权重
    weights = gaussian_kernel(distances, bandwidth)
    weights = weights / np.sum(weights)
    
    # 加权数据标准化
    weighted_mean = np.average(all_data, weights=weights, axis=0)
    centered_data = all_data - weighted_mean
    
    # 计算加权协方差矩阵
    weighted_cov = np.cov(centered_data.T, aweights=weights)
    
    # 特征值分解
    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)
    
    # 按特征值降序排列
    idx = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # 选择前n_components个主成分
    components = eigenvectors[:, :n_components]
    explained_variance = eigenvalues[:n_components]
    explained_variance_ratio = explained_variance / np.sum(eigenvalues)
    
    # 变换目标位置的数据
    target_idx = np.argmin(distances)
    target_data = all_data[target_idx] - weighted_mean
    transformed_data = np.dot(target_data, components)
    
    return components, explained_variance_ratio, transformed_data

print("GWPCA核心算法已定义")
print("- 使用高斯核函数计算空间权重")
print("- 支持局部加权协方差矩阵计算")
print("- 输出前3个主成分")

# 执行GWPCA分析
print("\n=== 执行GWPCA分析 ===")

# 设置参数
bandwidth = 3.0  # 带宽参数（度）
n_components = 3  # 主成分数量

print(f"GWPCA参数设置：")
print(f"- 带宽：{bandwidth}度")
print(f"- 主成分数量：{n_components}")
print(f"- 核函数：高斯核")

# 选择一些代表性位置进行分析
test_locations = [
    [75, 35],   # 西部
    [90, 40],   # 中心
    [105, 37],  # 东部
    [85, 42],   # 北部
    [95, 33]    # 南部
]

results = []

print(f"\n开始处理 {len(test_locations)} 个位置...")

# 为每个位置执行GWPCA
for i, target_coord in enumerate(test_locations):
    print(f"处理位置 {i+1}: [{target_coord[0]}, {target_coord[1]}]")
    
    try:
        components, var_ratios, transformed = gwpca_at_location(
            target_coord, coordinates, embeddings_data, bandwidth, n_components=3
        )
        
        # 存储结果
        results.append({
            'location': target_coord,
            'components': components,
            'var_ratios': var_ratios,
            'transformed': transformed
        })
        
        print(f"  - 解释方差比: [{var_ratios[0]:.3f}, {var_ratios[1]:.3f}, {var_ratios[2]:.3f}]")
        print(f"  - 累计解释方差: {var_ratios.sum():.4f}")
        print(f"  - 降维后数据: [{transformed[0]:.3f}, {transformed[1]:.3f}, {transformed[2]:.3f}]")
        
    except Exception as e:
        print(f"  - 位置 {i} 处理失败: {e}")

print(f"\nGWPCA分析完成！")
print(f"- 成功处理位置数：{len(results)}")

if len(results) > 0:
    pc1_values = [r['transformed'][0] for r in results]
    pc2_values = [r['transformed'][1] for r in results]
    pc3_values = [r['transformed'][2] for r in results]
    
    print(f"- 第一主成分范围：{min(pc1_values):.3f} - {max(pc1_values):.3f}")
    print(f"- 第二主成分范围：{min(pc2_values):.3f} - {max(pc2_values):.3f}")
    print(f"- 第三主成分范围：{min(pc3_values):.3f} - {max(pc3_values):.3f}")
    
    avg_var_ratios = np.mean([r['var_ratios'] for r in results], axis=0)
    print(f"- 平均解释方差比：PC1={avg_var_ratios[0]:.3f}, PC2={avg_var_ratios[1]:.3f}, PC3={avg_var_ratios[2]:.3f}")

# 可视化结果
print("\n=== 结果可视化 ===")

if len(results) > 0:
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('地理加权主成分分析(GWPCA)结果', fontsize=16)
    
    # 提取结果数据
    locations = np.array([r['location'] for r in results])
    pc1_values = [r['transformed'][0] for r in results]
    pc2_values = [r['transformed'][1] for r in results]
    pc3_values = [r['transformed'][2] for r in results]
    var_ratios_1 = [r['var_ratios'][0] for r in results]
    var_ratios_2 = [r['var_ratios'][1] for r in results]
    var_ratios_3 = [r['var_ratios'][2] for r in results]
    
    lons = locations[:, 0]
    lats = locations[:, 1]
    
    # 绘制第一主成分
    scatter1 = axes[0, 0].scatter(lons, lats, c=pc1_values, cmap='viridis', s=100)
    axes[0, 0].set_title('第一主成分 (PC1)')
    axes[0, 0].set_xlabel('经度')
    axes[0, 0].set_ylabel('纬度')
    plt.colorbar(scatter1, ax=axes[0, 0])
    
    # 绘制第二主成分
    scatter2 = axes[0, 1].scatter(lons, lats, c=pc2_values, cmap='plasma', s=100)
    axes[0, 1].set_title('第二主成分 (PC2)')
    axes[0, 1].set_xlabel('经度')
    axes[0, 1].set_ylabel('纬度')
    plt.colorbar(scatter2, ax=axes[0, 1])
    
    # 绘制解释方差比
    x_pos = np.arange(len(results))
    width = 0.25
    
    axes[1, 0].bar(x_pos - width, var_ratios_1, width, label='PC1', alpha=0.8)
    axes[1, 0].bar(x_pos, var_ratios_2, width, label='PC2', alpha=0.8)
    axes[1, 0].bar(x_pos + width, var_ratios_3, width, label='PC3', alpha=0.8)
    axes[1, 0].set_title('各位置的解释方差比')
    axes[1, 0].set_xlabel('位置编号')
    axes[1, 0].set_ylabel('解释方差比')
    axes[1, 0].legend()
    axes[1, 0].set_xticks(x_pos)
    axes[1, 0].set_xticklabels([f'位置{i+1}' for i in range(len(results))])
    
    # 绘制累计解释方差
    cumulative_var = [sum(r['var_ratios']) for r in results]
    axes[1, 1].bar(x_pos, cumulative_var, alpha=0.8, color='orange')
    axes[1, 1].set_title('累计解释方差比')
    axes[1, 1].set_xlabel('位置编号')
    axes[1, 1].set_ylabel('累计解释方差比')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels([f'位置{i+1}' for i in range(len(results))])
    
    plt.tight_layout()
    plt.show()
    
    print("可视化完成")
else:
    print("没有有效结果可供可视化")

# 生成分析报告
print("\n=== GWPCA分析报告 ===")

if len(results) > 0:
    print(f"\n1. 数据概况:")
    print(f"   - 研究区域：丝绸之路流域 (70°-110°E, 30°-45°N)")
    print(f"   - 时间范围：2022年")
    print(f"   - 采样尺度：10km")
    print(f"   - 有效样本数：{len(embeddings_data)}")
    print(f"   - 原始维度：64维")
    print(f"   - 降维后维度：3维")
    
    print(f"\n2. GWPCA参数:")
    print(f"   - 空间权重函数：高斯核")
    print(f"   - 带宽参数：{bandwidth}度")
    print(f"   - 分析位置数：{len(results)}")
    
    print(f"\n3. 主成分统计:")
    for i in range(n_components):
        pc_values = [r['transformed'][i] for r in results]
        var_ratios = [r['var_ratios'][i] for r in results]
        print(f"   第{i+1}主成分 (PC{i+1}):")
        print(f"     - 数值范围：{min(pc_values):.4f} - {max(pc_values):.4f}")
        print(f"     - 平均解释方差比：{np.mean(var_ratios):.4f}")
        print(f"     - 标准差：{np.std(pc_values):.4f}")
    
    print(f"\n4. 总体性能:")
    avg_cumulative = np.mean([sum(r['var_ratios']) for r in results])
    print(f"   - 平均累计解释方差比：{avg_cumulative:.4f}")
    print(f"   - 信息保留率：{avg_cumulative*100:.2f}%")
    
    print(f"\n5. 空间变异性:")
    pc1_spatial_var = np.var([r['transformed'][0] for r in results])
    pc2_spatial_var = np.var([r['transformed'][1] for r in results])
    pc3_spatial_var = np.var([r['transformed'][2] for r in results])
    print(f"   - PC1空间方差：{pc1_spatial_var:.4f}")
    print(f"   - PC2空间方差：{pc2_spatial_var:.4f}")
    print(f"   - PC3空间方差：{pc3_spatial_var:.4f}")
    
    print(f"\n=== 分析完成 ===")
    print(f"\n算法特点:")
    print(f"- ✓ 考虑空间邻近性的局部主成分分析")
    print(f"- ✓ 保留了主要的空间变异模式")
    print(f"- ✓ 有效降维：从64维降至3维")
    print(f"- ✓ 空间自适应：不同位置有不同的主成分")
    print(f"- ✓ 稳健性强：避免了复杂的投影问题")
    
    print(f"\n使用建议:")
    print(f"- 可以调整带宽参数来控制空间权重的影响范围")
    print(f"- 可以增加采样点数量来提高分析精度")
    print(f"- 可以选择不同的时间范围进行对比分析")
    print(f"- 建议在实际应用中验证结果的地理意义")
    
else:
    print("分析失败，请检查数据和参数设置")