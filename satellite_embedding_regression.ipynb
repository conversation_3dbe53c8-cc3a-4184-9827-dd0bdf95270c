{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 使用卫星嵌入数据集进行回归 - Python版本\n", "\n", "基于Google Earth Engine教程转换而来\n", "https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-04-regression?hl=zh-cn\n", "\n", "本教程演示如何：\n", "1. 使用64维卫星嵌入作为回归的特征输入\n", "2. 预测地上生物量(AGB)\n", "3. 使用GEDI L4A数据作为训练目标\n", "4. 训练随机森林回归模型\n", "5. 生成生物量预测地图\n", "6. 估计总生物量存量\n", "\n", "## 背景介绍\n", "\n", "嵌入字段可像用于分类一样，用作回归的特征输入/预测变量。在本教程中，我们将学习如何使用64D嵌入字段层作为输入，进行预测地上生物量(AGB)的多元回归分析。\n", "\n", "NASA的Global Ecosystem Dynamics Investigation (GEDI)任务沿地面样线以30米的空间分辨率每隔60米收集一次LIDAR测量数据。我们将使用GEDI L4A地上生物量密度栅格数据集，其中包含地上生物量密度(AGBD)的点估计值，这些值将用作回归模型中的预测变量。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图已创建\n"]}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "\n", "# 使用卫星底图\n", "Map.add_basemap('SATELLITE')\n", "\n", "print(\"地图已创建\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择区域\n", "\n", "我们选择印度西高止山脉中的一个区域作为研究区域，该区域具有丰富的植被覆盖。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已选择印度西高止山脉区域作为研究区域\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c0f11af81c94eb19c6a209712e587b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[14.873000000000001, 74.485], controls=(WidgetControl(options=['position', 'transparent_bg'], widge…"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义感兴趣的区域 - 印度西高止山脉\n", "geometry = ee.Geometry.Polygon([[\n", "    [74.322, 14.981],\n", "    [74.322, 14.765],\n", "    [74.648, 14.765],\n", "    [74.648, 14.980]\n", "]])\n", "\n", "# 将地图中心设置到选定区域\n", "Map.centerObject(geometry)\n", "Map.addLayer(geometry, {'color': 'red'}, 'Study Area', False)\n", "\n", "print(\"已选择印度西高止山脉区域作为研究区域\")\n", "\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择时间段\n", "\n", "选择要运行回归分析的年份。卫星嵌入是按年间隔进行汇总的。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["分析年份：2022\n", "时间范围：2022-01-01 到 2023-01-01\n"]}], "source": ["# 选择分析年份\n", "year = 2022\n", "start_date = ee.Date.fromYMD(year, 1, 1)\n", "end_date = start_date.advance(1, 'year')\n", "\n", "print(f\"分析年份：{year}\")\n", "print(f\"时间范围：{year}-01-01 到 {year+1}-01-01\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 准备卫星嵌入数据集\n", "\n", "64波段卫星嵌入图像将用作回归的预测变量。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已加载卫星嵌入数据集\n", "- 过滤年份：2022\n", "- 过滤区域：印度西高止山脉\n"]}], "source": ["# 加载卫星嵌入数据集\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "embeddings_filtered = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "print(\"已加载卫星嵌入数据集\")\n", "print(f\"- 过滤年份：{year}\")\n", "print(\"- 过滤区域：印度西高止山脉\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已创建卫星嵌入镶嵌图\n", "- 保留了原始UTM投影\n", "- 波段数量：64个（A00到A63）\n"]}], "source": ["# 提取投影信息并创建镶嵌图\n", "# 从第一个图块中提取投影信息\n", "embeddings_projection = ee.Image(embeddings_filtered.first()).select(0).projection()\n", "\n", "# 设置镶嵌图的投影\n", "embeddings_image = embeddings_filtered.mosaic() \\\n", "    .setDefaultProjection(embeddings_projection)\n", "\n", "print(\"已创建卫星嵌入镶嵌图\")\n", "print(\"- 保留了原始UTM投影\")\n", "print(\"- 波段数量：64个（A00到A63）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 准备GEDI L4A镶嵌\n", "\n", "由于GEDI生物量估计值将用于训练我们的回归模型，因此在使用之前过滤掉无效或不可靠的GEDI数据至关重要。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已定义GEDI数据质量过滤函数\n", "- 质量过滤：l4_quality_flag = 1 且 degrade_flag = 0\n", "- 误差过滤：相对标准误差 ≤ 50%\n"]}], "source": ["# 加载GEDI L4A数据集\n", "gedi = ee.ImageCollection('LARSE/GEDI/GEDI04_A_002_MONTHLY')\n", "\n", "# 定义质量掩膜函数\n", "def quality_mask(image):\n", "    \"\"\"选择最高质量的GEDI数据\"\"\"\n", "    return image.updateMask(image.select('l4_quality_flag').eq(1)) \\\n", "        .updateMask(image.select('degrade_flag').eq(0))\n", "\n", "# 定义误差掩膜函数\n", "def error_mask(image):\n", "    \"\"\"掩膜相对标准误差>50%的不可靠GEDI测量\"\"\"\n", "    relative_se = image.select('agbd_se').divide(image.select('agbd'))\n", "    return image.updateMask(relative_se.lte(0.5))\n", "\n", "print(\"已定义GEDI数据质量过滤函数\")\n", "print(\"- 质量过滤：l4_quality_flag = 1 且 degrade_flag = 0\")\n", "print(\"- 误差过滤：相对标准误差 ≤ 50%\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已定义坡度掩膜函数\n", "- 使用Copernicus GLO-30 DEM\n", "- 排除坡度>30%的区域\n"]}], "source": ["# 定义坡度掩膜函数\n", "def slope_mask(image):\n", "    \"\"\"掩膜坡度>30%的GEDI测量\"\"\"\n", "    # 使用Copernicus GLO-30 DEM计算坡度\n", "    glo30 = ee.ImageCollection('COPERNICUS/DEM/GLO30')\n", "    \n", "    glo30_filtered = glo30 \\\n", "        .filter(ee.Filter.bounds(geometry)) \\\n", "        .select('DEM')\n", "    \n", "    # 提取投影\n", "    dem_proj = glo30_filtered.first().select(0).projection()\n", "    \n", "    # 创建镶嵌图并设置投影\n", "    elevation = glo30_filtered.mosaic().rename('dem') \\\n", "        .setDefaultProjection(dem_proj)\n", "    \n", "    # 计算坡度\n", "    slope = ee.Terrain.slope(elevation)\n", "    \n", "    return image.updateMask(slope.lt(30))\n", "\n", "print(\"已定义坡度掩膜函数\")\n", "print(\"- 使用Copernicus GLO-30 DEM\")\n", "print(\"- 排除坡度>30%的区域\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已处理GEDI L4A数据\n", "- 应用了质量、误差和坡度过滤\n", "- 已添加到地图进行可视化\n"]}], "source": ["# 应用过滤和处理GEDI数据\n", "gedi_filtered = gedi \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "gedi_projection = ee.Image(gedi_filtered.first()) \\\n", "    .select('agbd').projection()\n", "\n", "gedi_processed = gedi_filtered \\\n", "    .map(quality_mask) \\\n", "    .map(error_mask) \\\n", "    .map(slope_mask)\n", "\n", "gedi_mosaic = gedi_processed.mosaic() \\\n", "    .select('agbd').setDefaultProjection(gedi_projection)\n", "\n", "# 可视化GEDI镶嵌图\n", "gedi_vis = {\n", "    'min': 0,\n", "    'max': 200,\n", "    'palette': ['#edf8fb','#b2e2e2','#66c2a4','#2ca25f','#006d2c'],\n", "    'bands': ['agbd']\n", "}\n", "\n", "# 可视化时裁剪到研究区域\n", "Map.addLayer(gedi_mosaic.clip(geometry), gedi_vis, 'GEDI L4A (Filtered)', False)\n", "Map.addLayer(gedi_filtered.mosaic().clip(geometry), gedi_vis, 'GEDI L4A', False)\n", "\n", "print(\"已处理GEDI L4A数据\")\n", "print(\"- 应用了质量、误差和坡度过滤\")\n", "print(\"- 已添加到地图进行可视化\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c0f11af81c94eb19c6a209712e587b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=240885.0, center=[14.641389333859697, 74.49623107910158], controls=(WidgetControl(options=['positio…"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 对输入进行重采样和聚合\n", "\n", "在对像素进行抽样以训练回归模型之前，我们会对输入进行重采样和重新投影，使其位于同一像素网格中。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["重采样参数设置：\n", "- 网格尺度：100米\n", "- 投影：EPSG:3857 (Web Mercator)\n", "- 重采样方法：双线性插值\n"]}], "source": ["# 选择网格大小和投影\n", "grid_scale = 100\n", "grid_projection = ee.Projection('EPSG:3857').atScale(grid_scale)\n", "\n", "# 创建包含预测变量和被预测变量的堆叠图像\n", "stacked = embeddings_image.addBands(gedi_mosaic)\n", "\n", "# 设置重采样模式\n", "stacked = stacked.resample('bilinear')\n", "\n", "print(f\"重采样参数设置：\")\n", "print(f\"- 网格尺度：{grid_scale}米\")\n", "print(\"- 投影：EPSG:3857 (Web Mercator)\")\n", "print(\"- 重采样方法：双线性插值\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已完成重采样和聚合\n", "- 聚合方法：平均值\n", "- 已更新掩膜移除透明像素\n", "\n", "注意：对于大区域，建议将结果导出为资产以避免计算超时\n"]}], "source": ["# 使用平均值统计聚合像素\n", "stacked_resampled = stacked \\\n", "    .reduceResolution(**{\n", "        'reducer': ee.Reducer.mean(),\n", "        'maxPixels': 1024\n", "    }) \\\n", "    .reproject(**{\n", "        'crs': grid_projection\n", "    })\n", "\n", "# 更新掩膜以移除透明度\n", "stacked_resampled = stacked_resampled \\\n", "    .updateMask(stacked_resampled.mask().gt(0))\n", "\n", "print(\"已完成重采样和聚合\")\n", "print(\"- 聚合方法：平均值\")\n", "print(\"- 已更新掩膜移除透明像素\")\n", "print(\"\\n注意：对于大区域，建议将结果导出为资产以避免计算超时\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 提取训练特征\n", "\n", "我们在回归模型中使用卫星嵌入频段作为因变量（预测变量），并使用GEDI AGBD值作为自变量（被预测变量）。"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["变量定义：\n", "- 预测变量：64个卫星嵌入波段（A00-A63）\n", "- 被预测变量：agbd（地上生物量密度）\n", "已分离预测变量和被预测变量图像\n"]}], "source": ["# 定义预测变量和被预测变量\n", "predictors = embeddings_image.bandNames()\n", "predicted = gedi_mosaic.bandNames().get(0)\n", "\n", "print(\"变量定义：\")\n", "print(f\"- 预测变量：64个卫星嵌入波段（A00-A63）\")\n", "print(f\"- 被预测变量：{predicted.getInfo()}（地上生物量密度）\")\n", "\n", "predictor_image = stacked_resampled.select(predictors)\n", "predicted_image = stacked_resampled.select([predicted])\n", "\n", "print(\"已分离预测变量和被预测变量图像\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["训练特征提取完成\n", "- 目标样本数：1000\n", "- 实际提取样本数：1000\n", "- 采样尺度：100米\n", "- 已排除null值\n"]}], "source": ["# 使用分层采样确保从非掩膜像素进行采样\n", "class_mask = predicted_image.mask().toInt().rename('class')\n", "\n", "num_samples = 1000\n", "\n", "# 设置classPoints为[0, num_samples]\n", "# 这将给我们0个点用于类别0（掩膜区域）\n", "# 和num_samples个点用于类别1（非掩膜区域）\n", "training = stacked_resampled.addBands(class_mask) \\\n", "    .stratifiedSample(**{\n", "        'numPoints': num_samples,\n", "        'classBand': 'class',\n", "        'region': geometry,\n", "        'scale': grid_scale,\n", "        'classValues': [0, 1],\n", "        'classPoints': [0, num_samples],\n", "        'dropNulls': True,\n", "        'tileScale': 16\n", "    })\n", "\n", "print(f\"训练特征提取完成\")\n", "print(f\"- 目标样本数：{num_samples}\")\n", "print(f\"- 实际提取样本数：{training.size().getInfo()}\")\n", "print(f\"- 采样尺度：{grid_scale}米\")\n", "print(\"- 已排除null值\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c0f11af81c94eb19c6a209712e587b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=240651.0, center=[14.79679147940089, 74.47837829589845], controls=(WidgetControl(options=['position…"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将训练点添加到地图\n", "Map.addLayer(training, {'color': 'blue'}, 'Extracted Samples', False)\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练回归模型\n", "\n", "使用随机森林分类器的回归模式来训练模型。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["随机森林回归模型训练完成\n", "- 模型类型：随机森林回归\n", "- 树的数量：50\n", "- 输入特征：64维卫星嵌入向量\n", "- 目标变量：地上生物量密度(AGBD)\n"]}], "source": ["# 使用随机森林分类器并设置输出模式为回归\n", "model = ee.Classifier.smileRandomForest(50) \\\n", "    .setOutputMode('REGRESSION') \\\n", "    .train(**{\n", "        'features': training,\n", "        'classProperty': predicted,\n", "        'inputProperties': predictors\n", "    })\n", "\n", "print(\"随机森林回归模型训练完成\")\n", "print(\"- 模型类型：随机森林回归\")\n", "print(\"- 树的数量：50\")\n", "print(\"- 输入特征：64维卫星嵌入向量\")\n", "print(\"- 目标变量：地上生物量密度(AGBD)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 模型性能评估\n", "\n", "计算模型的预测值与输入值，并计算RMSE和相关系数来检查模型性能。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已获取训练样本的预测值\n", "- 预测列名：agbd_predicted\n", "- 观测列名：agbd\n"]}], "source": ["# 获取模型对训练样本的预测\n", "predicted_training = training.classify(**{\n", "    'classifier': model,\n", "    'outputName': 'agbd_predicted'\n", "})\n", "\n", "print(\"已获取训练样本的预测值\")\n", "print(\"- 预测列名：agbd_predicted\")\n", "print(\"- 观测列名：agbd\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["模型性能评估：\n", "- RMSE: 28.53 Mg/ha\n", "\n", "注意：可以创建观测值vs预测值的散点图来进一步评估模型性能\n", "在实际应用中，建议使用独立的验证数据集\n"]}], "source": ["# 计算RMSE\n", "def calculate_rmse(input_fc):\n", "    \"\"\"计算均方根误差\"\"\"\n", "    observed = ee.Array(input_fc.aggregate_array('agbd'))\n", "    predicted = ee.Array(input_fc.aggregate_array('agbd_predicted'))\n", "    rmse = observed.subtract(predicted).pow(2) \\\n", "        .reduce('mean', [0]).sqrt().get([0])\n", "    return rmse\n", "\n", "rmse = calculate_rmse(predicted_training)\n", "\n", "print(f\"模型性能评估：\")\n", "print(f\"- RMSE: {rmse.getInfo():.2f} Mg/ha\")\n", "print(\"\\n注意：可以创建观测值vs预测值的散点图来进一步评估模型性能\")\n", "print(\"在实际应用中，建议使用独立的验证数据集\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 生成预测结果\n", "\n", "使用训练好的模型对整个研究区域生成地上生物量密度预测。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已生成地上生物量密度预测地图\n", "- 单位：Mg/ha（每公顷兆克）\n", "- 深绿色表示预测的生物质密度较高\n", "- 浅色表示预测的生物质密度较低\n"]}], "source": ["# 对整个区域进行预测\n", "predicted_image = stacked_resampled.classify(**{\n", "    'classifier': model,\n", "    'outputName': 'agbd'\n", "})\n", "\n", "# 可视化预测结果\n", "predicted_vis = {\n", "    'min': 0,\n", "    'max': 200,\n", "    'palette': ['#edf8fb','#b2e2e2','#66c2a4','#2ca25f','#006d2c'],\n", "    'bands': ['agbd']\n", "}\n", "\n", "Map.addLayer(predicted_image, predicted_vis, 'Predicted AGBD')\n", "\n", "print(\"已生成地上生物量密度预测地图\")\n", "print(\"- 单位：Mg/ha（每公顷兆克）\")\n", "print(\"- 深绿色表示预测的生物质密度较高\")\n", "print(\"- 浅色表示预测的生物质密度较低\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c0f11af81c94eb19c6a209712e587b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=240663.0, center=[14.788824836033049, 74.49348449707033], controls=(WidgetControl(options=['positio…"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 估计总生物量\n", "\n", "计算该区域的地上总生物量(AGB)存量。首先需要移除所有属于非植被区域的像素。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已加载ESA WorldCover地表覆盖数据\n", "- 版本：v200\n", "- 聚合方法：众数（mode）\n"]}], "source": ["# 使用ESA WorldCover地表覆盖数据集选择植被像素\n", "worldcover = ee.ImageCollection('ESA/WorldCover/v200').first()\n", "\n", "# 将像素聚合到与其他数据集相同的网格\n", "# 使用'mode'值（即网格内出现频率最高的土地覆盖）\n", "worldcover_resampled = worldcover \\\n", "    .reduceResolution(**{\n", "        'reducer': ee.Reducer.mode(),\n", "        'maxPixels': 1024\n", "    }) \\\n", "    .reproject(**{\n", "        'crs': grid_projection\n", "    })\n", "\n", "print(\"已加载ESA WorldCover地表覆盖数据\")\n", "print(\"- 版本：v200\")\n", "print(\"- 聚合方法：众数（mode）\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已应用植被掩膜\n", "- 包含类别：森林、灌木地、草地、农田、红树林\n", "- 排除类别：水体、建筑、裸地等\n"]}], "source": ["# 选择以下植被类别的网格：\n", "# | 类别名称 | 值 |\n", "# | 森林     | 10 |\n", "# | 灌木地   | 20 |\n", "# | 草地     | 30 |\n", "# | 农田     | 40 |\n", "# | 红树林   | 95 |\n", "\n", "land_cover_mask = worldcover_resampled.eq(10) \\\n", "    .Or(worldcover_resampled.eq(20)) \\\n", "    .Or(worldcover_resampled.eq(30)) \\\n", "    .Or(worldcover_resampled.eq(40)) \\\n", "    .Or(worldcover_resampled.eq(95))\n", "\n", "predicted_image_masked = predicted_image.updateMask(land_cover_mask)\n", "\n", "Map.addLayer(predicted_image_masked, predicted_vis, 'Predicted AGBD (Masked)')\n", "\n", "print(\"已应用植被掩膜\")\n", "print(\"- 包含类别：森林、灌木地、草地、农田、红树林\")\n", "print(\"- 排除类别：水体、建筑、裸地等\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["总地上生物量估算：\n", "- 总AGB：11076864 Mg\n", "- 研究区域：印度西高止山脉选定区域\n", "- 计算方法：像素AGBD × 像素面积，然后求和\n"]}], "source": ["# 计算总地上生物量\n", "# GEDI AGBD值的单位为每公顷兆克(Mg/ha)\n", "# 将每个像素乘以其面积（以公顷为单位），然后求和\n", "\n", "pixel_area_ha = ee.Image.pixelArea().divide(10000)\n", "predicted_agb = predicted_image_masked.multiply(pixel_area_ha)\n", "\n", "stats = predicted_agb.reduceRegion(**{\n", "    'reducer': ee.Reducer.sum(),\n", "    'geometry': geometry,\n", "    'scale': grid_scale,\n", "    'maxPixels': 1e10,\n", "    'tileScale': 16\n", "})\n", "\n", "# 结果是一个字典，每个波段都有一个键\n", "total_agb = stats.getNumber('agbd')\n", "\n", "print(f\"总地上生物量估算：\")\n", "print(f\"- 总AGB：{total_agb.getInfo():.0f} Mg\")\n", "print(f\"- 研究区域：印度西高止山脉选定区域\")\n", "print(f\"- 计算方法：像素AGBD × 像素面积，然后求和\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 显示交互式地图\n", "\n", "地图包含以下图层，可以切换查看不同结果："]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图图层说明：\n", "1. Study Area - 研究区域边界（红色）\n", "2. GEDI L4A (Filtered) - 过滤后的GEDI生物量数据\n", "3. Predicted AGBD - 预测的地上生物量密度\n", "4. Predicted AGBD (Masked) - 应用植被掩膜后的预测结果\n", "\n", "颜色说明：\n", "- 浅蓝色：低生物量密度（0-50 Mg/ha）\n", "- 绿色：中等生物量密度（50-100 Mg/ha）\n", "- 深绿色：高生物量密度（100-200 Mg/ha）\n", "\n", "可以在地图界面中切换不同图层进行比较分析\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c0f11af81c94eb19c6a209712e587b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=120484.0, center=[14.785505314974664, 74.51889038085939], controls=(WidgetControl(options=['positio…"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"地图图层说明：\")\n", "print(\"1. Study Area - 研究区域边界（红色）\")\n", "print(\"2. GEDI L4A (Filtered) - 过滤后的GEDI生物量数据\")\n", "print(\"3. Predicted AGBD - 预测的地上生物量密度\")\n", "print(\"4. Predicted AGBD (Masked) - 应用植被掩膜后的预测结果\")\n", "print(\"\\n颜色说明：\")\n", "print(\"- 浅蓝色：低生物量密度（0-50 Mg/ha）\")\n", "print(\"- 绿色：中等生物量密度（50-100 Mg/ha）\")\n", "print(\"- 深绿色：高生物量密度（100-200 Mg/ha）\")\n", "print(\"\\n可以在地图界面中切换不同图层进行比较分析\")\n", "\n", "# 显示地图\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "本教程展示了如何使用卫星嵌入数据集进行回归分析：\n", "\n", "### 主要步骤\n", "1. **区域选择** - 选择了印度西高止山脉作为研究区域\n", "2. **数据准备** - 加载和过滤卫星嵌入数据集和GEDI L4A数据\n", "3. **数据质量控制** - 应用质量、误差和坡度过滤\n", "4. **重采样聚合** - 统一像素网格和投影\n", "5. **特征提取** - 分层采样获取训练数据\n", "6. **模型训练** - 使用随机森林回归模型\n", "7. **预测生成** - 对整个区域生成生物量预测\n", "8. **总量估算** - 计算区域总地上生物量\n", "\n", "### 关键优势\n", "- **多源数据融合** - 结合卫星嵌入和LIDAR数据\n", "- **高精度预测** - 利用64维嵌入向量的丰富信息\n", "- **空间连续性** - 生成连续的生物量分布地图\n", "- **可扩展性** - 方法可应用于其他区域和生态系统\n", "\n", "### 应用前景\n", "- 森林碳储量评估\n", "- 生态系统服务量化\n", "- 气候变化影响监测\n", "- 可持续森林管理"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}