# 导入必要的库
import ee
import geemap

# 初始化Earth Engine
geemap.set_proxy(port=33210)
ee.Initialize()

print("Earth Engine已初始化")

# 创建交互式地图
Map = geemap.Map()

# 使用卫星底图
Map.add_basemap('SATELLITE')

print("地图已创建")

# 选择区域
print("\n1. 选择研究区域")
path = r'G:\Heritage\Changcheng\陕西神木_1km_part8.shp'
roi = geemap.shp_to_ee(path)

geometry = roi.geometry().bounds()

Map.centerObject(geometry, 12)
Map.addLayer(geometry, {'color': 'red'}, 'Selected Region', False)
print("已选择陕西神木作为研究区域")

# 准备卫星嵌入数据集
print("\n2. 准备卫星嵌入数据集")
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

year = 2022
start_date = ee.Date.fromYMD(year, 1, 1)
end_date = start_date.advance(1, 'year')

filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))

embeddings_image = filtered_embeddings.mosaic()
print(f"已加载{year}年的卫星嵌入数据")

# 准备聚类图像（使用完整的嵌入数据，不应用掩码）
print("\n3. 准备聚类图像")
cluster_image = embeddings_image
print("已准备聚类图像（包含研究区所有像元）")

# 提取训练样本
print("\n4. 提取训练样本")

training = cluster_image.sample(**{
    'region': geometry,
    'scale': 10,
    'numPixels': 1000,
    'seed': 100,
    'dropNulls': True,
    'geometries': True
})

Map.addLayer(training, {'color': 'blue'}, 'Extracted Samples', False)
print("已完成随机采样，提取1000个训练样本")

# 执行无监督聚类
print("\n5. 执行无监督聚类")
min_clusters = 4
max_clusters = 6

clusterer = ee.Clusterer.wekaCascadeKMeans(**{
    'minClusters': min_clusters, 
    'maxClusters': max_clusters
}).train(**{
    'features': training,
    'inputProperties': cluster_image.bandNames()
})

clustered = cluster_image.cluster(clusterer)
print(f"已完成聚类分析（{min_clusters}-{max_clusters}个聚类）")

# 可视化聚类结果
print("\n6. 可视化聚类结果")
# 直接显示聚类结果，不进行标签分配
Map.addLayer(clustered.randomVisualizer().clip(geometry), {}, 'Unsupervised Clusters')
print("已添加无监督聚类结果到地图")

print("\n=== 无监督分类完成 ===")
print("地图图层说明：")
print("1. Selected Region - 研究区域边界")
print("2. Extracted Samples - 训练样本点")
print("3. Unsupervised Clusters - 无监督聚类结果")
print("\n说明：聚类结果显示了研究区内不同地物类型的空间分布")

# 返回地图对象
Map



path = r'G:\Heritage\Changcheng\陕西神木_1km_part8.shp'
roi = geemap.shp_to_ee(path)

geometry = roi.geometry().bounds()

# # 定义感兴趣的区域 - 肯尼亚海岸线
# geometry = ee.Geometry.Polygon([[
#     [39.4926, -4.39833],
#     [39.4926, -4.47394],
#     [39.5491, -4.47394],
#     [39.5491, -4.39833]
# ]])


# 将地图中心设置到选定区域
Map.centerObject(geometry)
Map.addLayer(geometry, {'color': 'red'}, 'Search Area')

print("已选择堪萨斯州富兰克林县作为搜索区域")

Map

# 导入本地shapefile作为sample_points
import pandas as pd

# 使用geemap导入本地shapefile
sample_points_fc = geemap.shp_to_ee(r'G:\Heritage\Changcheng\test\great_wall_test02.shp')

# 将FeatureCollection转换为坐标列表
sample_points_list = sample_points_fc.getInfo()['features']
sample_points = []

for feature in sample_points_list:
    coords = feature['geometry']['coordinates']
    sample_points.append([coords[0], coords[1]])  # [经度, 纬度]

print(f"已成功导入{len(sample_points)}个样本点")
print("样本点坐标预览:")
for i, point in enumerate(sample_points[:3]):  # 显示前3个点
    print(f"  点{i+1}: [{point[0]:.6f}, {point[1]:.6f}]")

# 创建参考位置样本
# 注意：在实际应用中，您应该使用geemap的交互式工具选择参考位置
# 这里我们创建一些示例参考点（基于谷仓的典型位置）

# sample_points = [
#     [-95.2500, 38.7200],  # 示例谷仓位置1
#     [-95.2300, 38.7100],  # 示例谷仓位置2
#     [-95.2100, 38.6900]   # 示例谷仓位置3
# ]

# 创建FeatureCollection
features = []
for i, point in enumerate(sample_points):
    feature = ee.Feature(
        ee.Geometry.Point(point),
        {'id': i + 1}
    )
    features.append(feature)

samples = ee.FeatureCollection(features)

# 添加参考点到地图
Map.addLayer(samples, {'color': 'yellow'}, 'Reference Locations')

print(f"已创建{len(sample_points)}个参考位置")
print("- 这些位置将用于提取嵌入向量")
print("- 在实际应用中，请使用高分辨率影像准确标记谷仓位置")

# 选择搜索年份
year = 2024
start_date = ee.Date.fromYMD(year, 1, 1)
end_date = start_date.advance(1, 'year')

print(f"搜索年份：{year}")
print(f"时间范围：{year}-01-01 到 {year+1}-01-01")
print("\n注意：卫星嵌入图像源自公共领域的地球观测数据集")
print("对于变化较大的地区，请验证所选年份的相应位置是否存在该对象")

# 加载卫星嵌入数据集
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')

mosaic = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .mosaic()

print("已加载和处理卫星嵌入数据集")
print(f"- 过滤年份：{year}")
print("- 已创建镶嵌图")
print("- 波段数量：64个（A00到A63）")

# 选择提取和比较嵌入向量的尺度
# 卫星嵌入的原始分辨率为10米，对于识别谷仓等小型物体是合适的
# 如果您想寻找较大的物体，可以使用较大的值（如20米、100米）
scale = 10

# 从样本中提取嵌入向量
sample_embeddings = mosaic.sampleRegions(**{
    'collection': samples,
    'scale': scale
})

print(f"嵌入向量提取完成")
print(f"- 采样尺度：{scale}米")
print(f"- 参考样本数：{sample_embeddings.size().getInfo()}")
print("- 每个样本包含64维嵌入向量")
print("\n卫星嵌入的优势：")
print("- 可线性组合，保留距离关系")
print("- 适合不同尺度的对象检测")

# 计算每个参考位置与所有像素之间的点积
band_names = mosaic.bandNames()

sample_distances = ee.ImageCollection(sample_embeddings.map(lambda f: (
    ee.Image(f.toArray(band_names)).arrayFlatten([band_names])
    .multiply(mosaic)
    .reduce('sum')
    .rename('similarity')
)))

# 计算所有参考位置的平均距离
mean_distance = sample_distances.mean()

print("相似度计算完成")
print("- 方法：点积（dot product）")
print("- 点积接近1：相似的嵌入向量（角度接近0）")
print("- 点积接近-1：差异较大的向量（正交或相反方向）")
print("- 已计算所有参考位置的平均相似度")

# 定义相似度可视化参数
palette = [
    '000004', '2C105C', '711F81', 'B63679',
    'EE605E', 'FDAE78', 'FCFDBF', 'FFFFFF'
]

similarity_vis = {'palette': palette, 'min': 0, 'max': 1}

# 添加相似度图层到地图
Map.addLayer(
    mean_distance.clip(geometry), 
    similarity_vis,
    'Similarity (bright = close)', 
    False
)

print("已添加相似度可视化图层")
print("- 亮色区域：与参考位置相似度高")
print("- 暗色区域：与参考位置相似度低")
print("- 可以通过调整图层透明度来叠加查看")

Map

# 应用阈值
# 合适的阈值取决于您要查找的对象和比例
# 您可以尝试不同的值，看看哪个值最适合您的应用
threshold = 0.955

similar_pixels = mean_distance.gt(threshold)

print(f"相似度阈值设置：{threshold}")
print("- 阈值越高：匹配越严格，结果越少但精度越高")
print("- 阈值越低：匹配越宽松，结果越多但可能包含误报")
print("- 建议根据实际应用调整阈值")

# 将结果转换为多边形
# 使用selfMask()掩膜0值，只获取匹配像素的多边形
polygons = similar_pixels.selfMask().reduceToVectors(**{
    'scale': scale,
    'eightConnected': False,
    'maxPixels': 1e10,
    'geometry': geometry
})

print("已将相似像素转换为多边形")
print(f"- 矢量化尺度：{scale}米")
print("- 连接方式：4连通")
print("- 每个多边形代表一个匹配的站点")

# 提取矢量化多边形的质心
predicted_matches = polygons.map(lambda f: f.centroid(**{'maxError': 1}))

print("已提取匹配位置的质心")
print(f"- 预测匹配数量：{predicted_matches.size().getInfo()}")
print("- 每个点代表一个潜在的谷仓位置")
print("- 质心提供了匹配区域的中心位置")

# 添加预测匹配到地图
Map.addLayer(predicted_matches, {'color': 'cyan'}, 'Predicted Matches')

print("已添加预测匹配位置到地图")
print("- 青色点：预测的谷仓位置")
print("- 黄色点：原始参考位置")
print("- 红色边界：搜索区域")

# 计算匹配统计信息
num_matches = predicted_matches.size()
search_area_km2 = geometry.area().divide(1000000)  # 转换为平方公里

print("结果统计：")
print(f"- 总匹配数量：{num_matches.getInfo()}")
print(f"- 搜索区域面积：{search_area_km2.getInfo():.1f} 平方公里")
print(f"- 匹配密度：{(num_matches.divide(search_area_km2)).getInfo():.2f} 个/平方公里")

print("\n结果评估建议：")
print("1. 放大每个预测匹配位置，检查是否确实有谷仓")
print("2. 观察匹配位置是否与参考位置有相似特征")
print("3. 如果误报较多，可以提高阈值")
print("4. 如果漏检较多，可以降低阈值")
print("5. 可以添加更多参考位置来改善结果")

print("相似性搜索结果分析：")
print("\n✓ 正确匹配的特征：")
print("- 许多匹配项正确识别了谷仓")
print("- 匹配位置与参考位置有相似的形状和结构")
print("- 通常由相似材料制成，具有独特的嵌入向量")

print("\n⚠ 误报的可能原因：")
print("- 其他具有相似特征的建筑结构")
print("- 相似的光谱和空间特征")
print("- 阈值设置可能需要调整")

print("\n🔧 改进建议：")
print("1. 调整参考位置：选择更具代表性的谷仓样本")
print("2. 优化阈值：根据实际结果调整相似度阈值")
print("3. 多尺度分析：尝试不同的采样尺度")
print("4. 后处理：应用形态学操作去除噪声")
print("5. 验证数据：收集实地数据进行精度评估")

print("地图图层说明：")
print("1. Search Area - 搜索区域边界（红色）")
print("2. Reference Locations - 参考位置（黄色点）")
print("3. Similarity (bright = close) - 相似度热图")
print("4. Predicted Matches - 预测匹配位置（青色点）")
print("\n使用说明：")
print("- 可以切换图层查看不同信息")
print("- 放大查看具体匹配位置的详细情况")
print("- 对比参考位置和匹配位置的特征")
print("- 使用卫星底图验证结果的准确性")
print("\n可以在地图界面中进行交互式探索和验证")

# 显示地图
Map