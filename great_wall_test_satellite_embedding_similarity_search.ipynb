{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 使用卫星嵌入数据集进行相似性搜索 - Python版本\n", "\n", "基于Google Earth Engine教程转换而来\n", "https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-05-similarity-search?hl=zh-cn\n", "\n", "本教程演示如何：\n", "1. 使用卫星嵌入进行相似性搜索\n", "2. 查找特定对象的相似位置\n", "3. 计算嵌入向量之间的相似度\n", "4. 提取和验证匹配结果\n", "5. 应用于谷仓检测案例\n", "\n", "## 背景介绍\n", "\n", "嵌入提供了一个独特的机会，可使用地球观测数据查找相似的位置和特征。通过将参考位置的嵌入向量与嵌入图片的所有其他像素的嵌入向量进行比较，我们可以找到与参考位置具有相似属性的位置。在实践中，这使我们能够轻松找到感兴趣区域中的对象或特定类型的地点。\n", "\n", "在本教程中，我们将尝试查找某个区域中的所有谷仓。谷物筒仓或谷物仓是通常用于批量存储谷物的高大结构。它们通常出现在农场或加工厂。使用传统的遥感方法很难绘制这些结构，并且需要训练自定义对象检测模型。由于筒仓具有独特的形状和结构，并且通常由相似的材料制成，因此在卫星嵌入图像中，它们将由独特的嵌入向量表示，从而使我们能够通过简单的相似度搜索来定位它们。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图已创建\n"]}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "\n", "# 使用卫星底图\n", "Map.add_basemap('SATELLITE')\n", "\n", "print(\"地图已创建\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "1. 选择研究区域\n", "已选择爱荷华州Cerro Gordo County作为研究区域\n", "\n", "2. 准备卫星嵌入数据集\n", "已加载2022年的卫星嵌入数据\n", "\n", "3. 准备聚类图像\n", "已准备聚类图像（包含研究区所有像元）\n", "\n", "4. 提取训练样本\n", "已完成随机采样，提取1000个训练样本\n", "\n", "5. 执行无监督聚类\n", "已完成聚类分析（4-6个聚类）\n", "\n", "6. 可视化聚类结果\n", "已添加无监督聚类结果到地图\n", "\n", "=== 无监督分类完成 ===\n", "地图图层说明：\n", "1. Selected Region - 研究区域边界\n", "2. Extracted Samples - 训练样本点\n", "3. Unsupervised Clusters - 无监督聚类结果\n", "\n", "说明：聚类结果显示了研究区内不同地物类型的空间分布\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "24c93777b758492db2e7f2b38ba09a85", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=401363.0, center=[38.910166686370694, 110.48771927682162], controls=(WidgetControl(options=['positi…"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 选择区域\n", "print(\"\\n1. 选择研究区域\")\n", "path = r'G:\\Heritage\\Changcheng\\陕西神木_1km_part8.shp'\n", "roi = geemap.shp_to_ee(path)\n", "\n", "geometry = roi.geometry().bounds()\n", "\n", "Map.centerObject(geometry, 12)\n", "Map.addLayer(geometry, {'color': 'red'}, 'Selected Region', False)\n", "print(\"已选择陕西神木作为研究区域\")\n", "\n", "# 准备卫星嵌入数据集\n", "print(\"\\n2. 准备卫星嵌入数据集\")\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "year = 2022\n", "start_date = ee.Date.fromYMD(year, 1, 1)\n", "end_date = start_date.advance(1, 'year')\n", "\n", "filtered_embeddings = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "embeddings_image = filtered_embeddings.mosaic()\n", "print(f\"已加载{year}年的卫星嵌入数据\")\n", "\n", "# 准备聚类图像（使用完整的嵌入数据，不应用掩码）\n", "print(\"\\n3. 准备聚类图像\")\n", "cluster_image = embeddings_image\n", "print(\"已准备聚类图像（包含研究区所有像元）\")\n", "\n", "# 提取训练样本\n", "print(\"\\n4. 提取训练样本\")\n", "\n", "training = cluster_image.sample(**{\n", "    'region': geometry,\n", "    'scale': 10,\n", "    'numPixels': 1000,\n", "    'seed': 100,\n", "    'dropNulls': True,\n", "    'geometries': True\n", "})\n", "\n", "Map.addLayer(training, {'color': 'blue'}, 'Extracted Samples', False)\n", "print(\"已完成随机采样，提取1000个训练样本\")\n", "\n", "# 执行无监督聚类\n", "print(\"\\n5. 执行无监督聚类\")\n", "min_clusters = 4\n", "max_clusters = 6\n", "\n", "clusterer = ee.Clusterer.wekaCascadeKMeans(**{\n", "    'minClusters': min_clusters, \n", "    'maxClusters': max_clusters\n", "}).train(**{\n", "    'features': training,\n", "    'inputProperties': cluster_image.bandNames()\n", "})\n", "\n", "clustered = cluster_image.cluster(clusterer)\n", "print(f\"已完成聚类分析（{min_clusters}-{max_clusters}个聚类）\")\n", "\n", "# 可视化聚类结果\n", "print(\"\\n6. 可视化聚类结果\")\n", "# 直接显示聚类结果，不进行标签分配\n", "Map.addLayer(clustered.randomVisualizer().clip(geometry), {}, 'Unsupervised Clusters')\n", "print(\"已添加无监督聚类结果到地图\")\n", "\n", "print(\"\\n=== 无监督分类完成 ===\")\n", "print(\"地图图层说明：\")\n", "print(\"1. Selected Region - 研究区域边界\")\n", "print(\"2. Extracted Samples - 训练样本点\")\n", "print(\"3. Unsupervised Clusters - 无监督聚类结果\")\n", "print(\"\\n说明：聚类结果显示了研究区内不同地物类型的空间分布\")\n", "\n", "# 返回地图对象\n", "Map\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择搜索区域\n", "\n", "我们将绘制堪萨斯州富兰克林县的谷仓地图。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已选择堪萨斯州富兰克林县作为搜索区域\n"]}], "source": ["\n", "path = r'G:\\Heritage\\Changcheng\\陕西神木_1km_part8.shp'\n", "roi = geemap.shp_to_ee(path)\n", "\n", "geometry = roi.geometry().bounds()\n", "\n", "# # 定义感兴趣的区域 - 肯尼亚海岸线\n", "# geometry = ee.Geometry.Polygon([[\n", "#     [39.4926, -4.39833],\n", "#     [39.4926, -4.47394],\n", "#     [39.5491, -4.47394],\n", "#     [39.5491, -4.39833]\n", "# ]])\n", "\n", "\n", "# 将地图中心设置到选定区域\n", "Map.centerObject(geometry)\n", "Map.addLayer(geometry, {'color': 'red'}, 'Search Area')\n", "\n", "print(\"已选择堪萨斯州富兰克林县作为搜索区域\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aa2fd3e31d82415fbcd6f069df0b36b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[-4.436135270804017, 39.520849999999996], controls=(WidgetControl(options=['position', 'transparent…"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择参考位置\n", "\n", "我们需要标记一个或多个谷仓的位置作为参考。在实际应用中，您可以使用geemap的交互式工具来选择参考点。这里我们创建一些示例参考位置。\n", "\n", "**注意：** 在实际应用中，您应该使用高分辨率卫星影像来准确识别谷仓位置。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已成功导入3个样本点\n", "样本点坐标预览:\n", "  点1: [110.493387, 38.964568]\n", "  点2: [110.517966, 38.980378]\n", "  点3: [110.523942, 38.983818]\n"]}], "source": ["# 导入本地shapefile作为sample_points\n", "import pandas as pd\n", "\n", "# 使用geemap导入本地shapefile\n", "sample_points_fc = geemap.shp_to_ee(r'G:\\Heritage\\Changcheng\\test\\great_wall_test02.shp')\n", "\n", "# 将FeatureCollection转换为坐标列表\n", "sample_points_list = sample_points_fc.getInfo()['features']\n", "sample_points = []\n", "\n", "for feature in sample_points_list:\n", "    coords = feature['geometry']['coordinates']\n", "    sample_points.append([coords[0], coords[1]])  # [经度, 纬度]\n", "\n", "print(f\"已成功导入{len(sample_points)}个样本点\")\n", "print(\"样本点坐标预览:\")\n", "for i, point in enumerate(sample_points[:3]):  # 显示前3个点\n", "    print(f\"  点{i+1}: [{point[0]:.6f}, {point[1]:.6f}]\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已创建3个参考位置\n", "- 这些位置将用于提取嵌入向量\n", "- 在实际应用中，请使用高分辨率影像准确标记谷仓位置\n"]}], "source": ["# 创建参考位置样本\n", "# 注意：在实际应用中，您应该使用geemap的交互式工具选择参考位置\n", "# 这里我们创建一些示例参考点（基于谷仓的典型位置）\n", "\n", "# sample_points = [\n", "#     [-95.2500, 38.7200],  # 示例谷仓位置1\n", "#     [-95.2300, 38.7100],  # 示例谷仓位置2\n", "#     [-95.2100, 38.6900]   # 示例谷仓位置3\n", "# ]\n", "\n", "# 创建FeatureCollection\n", "features = []\n", "for i, point in enumerate(sample_points):\n", "    feature = ee.Feature(\n", "        ee.Geometry.Point(point),\n", "        {'id': i + 1}\n", "    )\n", "    features.append(feature)\n", "\n", "samples = ee.FeatureCollection(features)\n", "\n", "# 添加参考点到地图\n", "Map.addLayer(samples, {'color': 'yellow'}, 'Reference Locations')\n", "\n", "print(f\"已创建{len(sample_points)}个参考位置\")\n", "print(\"- 这些位置将用于提取嵌入向量\")\n", "print(\"- 在实际应用中，请使用高分辨率影像准确标记谷仓位置\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择时间段\n", "\n", "选择要运行搜索的年份。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["搜索年份：2024\n", "时间范围：2024-01-01 到 2025-01-01\n", "\n", "注意：卫星嵌入图像源自公共领域的地球观测数据集\n", "对于变化较大的地区，请验证所选年份的相应位置是否存在该对象\n"]}], "source": ["# 选择搜索年份\n", "year = 2024\n", "start_date = ee.Date.fromYMD(year, 1, 1)\n", "end_date = start_date.advance(1, 'year')\n", "\n", "print(f\"搜索年份：{year}\")\n", "print(f\"时间范围：{year}-01-01 到 {year+1}-01-01\")\n", "print(\"\\n注意：卫星嵌入图像源自公共领域的地球观测数据集\")\n", "print(\"对于变化较大的地区，请验证所选年份的相应位置是否存在该对象\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 过滤和镶嵌卫星嵌入数据集\n", "\n", "加载卫星嵌入数据集，过滤出所选年份的图片，并创建镶嵌图。"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已加载和处理卫星嵌入数据集\n", "- 过滤年份：2024\n", "- 已创建镶嵌图\n", "- 波段数量：64个（A00到A63）\n"]}], "source": ["# 加载卫星嵌入数据集\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "mosaic = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .mosaic()\n", "\n", "print(\"已加载和处理卫星嵌入数据集\")\n", "print(f\"- 过滤年份：{year}\")\n", "print(\"- 已创建镶嵌图\")\n", "print(\"- 波段数量：64个（A00到A63）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 从样本中提取嵌入向量\n", "\n", "我们在参考位置对镶嵌图进行采样，以获得与这些点相关联的嵌入向量。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["嵌入向量提取完成\n", "- 采样尺度：10米\n", "- 参考样本数：3\n", "- 每个样本包含64维嵌入向量\n", "\n", "卫星嵌入的优势：\n", "- 可线性组合，保留距离关系\n", "- 适合不同尺度的对象检测\n"]}], "source": ["# 选择提取和比较嵌入向量的尺度\n", "# 卫星嵌入的原始分辨率为10米，对于识别谷仓等小型物体是合适的\n", "# 如果您想寻找较大的物体，可以使用较大的值（如20米、100米）\n", "scale = 10\n", "\n", "# 从样本中提取嵌入向量\n", "sample_embeddings = mosaic.sampleRegions(**{\n", "    'collection': samples,\n", "    'scale': scale\n", "})\n", "\n", "print(f\"嵌入向量提取完成\")\n", "print(f\"- 采样尺度：{scale}米\")\n", "print(f\"- 参考样本数：{sample_embeddings.size().getInfo()}\")\n", "print(\"- 每个样本包含64维嵌入向量\")\n", "print(\"\\n卫星嵌入的优势：\")\n", "print(\"- 可线性组合，保留距离关系\")\n", "print(\"- 适合不同尺度的对象检测\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 计算相似度\n", "\n", "通过计算两个嵌入向量之间的点积，我们可以计算出它们的相似度。"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["相似度计算完成\n", "- 方法：点积（dot product）\n", "- 点积接近1：相似的嵌入向量（角度接近0）\n", "- 点积接近-1：差异较大的向量（正交或相反方向）\n", "- 已计算所有参考位置的平均相似度\n"]}], "source": ["# 计算每个参考位置与所有像素之间的点积\n", "band_names = mosaic.bandNames()\n", "\n", "sample_distances = ee.ImageCollection(sample_embeddings.map(lambda f: (\n", "    ee.Image(f.to<PERSON>y(band_names)).arrayFlatten([band_names])\n", "    .multiply(mosaic)\n", "    .reduce('sum')\n", "    .rename('similarity')\n", ")))\n", "\n", "# 计算所有参考位置的平均距离\n", "mean_distance = sample_distances.mean()\n", "\n", "print(\"相似度计算完成\")\n", "print(\"- 方法：点积（dot product）\")\n", "print(\"- 点积接近1：相似的嵌入向量（角度接近0）\")\n", "print(\"- 点积接近-1：差异较大的向量（正交或相反方向）\")\n", "print(\"- 已计算所有参考位置的平均相似度\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化相似度结果\n", "\n", "将平均距离图片添加到地图中来直观呈现结果。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已添加相似度可视化图层\n", "- 亮色区域：与参考位置相似度高\n", "- 暗色区域：与参考位置相似度低\n", "- 可以通过调整图层透明度来叠加查看\n"]}], "source": ["# 定义相似度可视化参数\n", "palette = [\n", "    '000004', '2C105C', '711F81', 'B63679',\n", "    'EE605E', 'FDAE78', 'FCFDBF', 'FFFFFF'\n", "]\n", "\n", "similarity_vis = {'palette': palette, 'min': 0, 'max': 1}\n", "\n", "# 添加相似度图层到地图\n", "Map.addLayer(\n", "    mean_distance.clip(geometry), \n", "    similarity_vis,\n", "    'Similarity (bright = close)', \n", "    False\n", ")\n", "\n", "print(\"已添加相似度可视化图层\")\n", "print(\"- 亮色区域：与参考位置相似度高\")\n", "print(\"- 暗色区域：与参考位置相似度低\")\n", "print(\"- 可以通过调整图层透明度来叠加查看\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aa2fd3e31d82415fbcd6f069df0b36b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=201019.0, center=[38.81456613562004, 110.46409606933595], controls=(WidgetControl(options=['positio…"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 提取匹配的地点\n", "\n", "定义一个阈值，并提取包含目标对象的位置。"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["相似度阈值设置：0.955\n", "- 阈值越高：匹配越严格，结果越少但精度越高\n", "- 阈值越低：匹配越宽松，结果越多但可能包含误报\n", "- 建议根据实际应用调整阈值\n"]}], "source": ["# 应用阈值\n", "# 合适的阈值取决于您要查找的对象和比例\n", "# 您可以尝试不同的值，看看哪个值最适合您的应用\n", "threshold = 0.955\n", "\n", "similar_pixels = mean_distance.gt(threshold)\n", "\n", "print(f\"相似度阈值设置：{threshold}\")\n", "print(\"- 阈值越高：匹配越严格，结果越少但精度越高\")\n", "print(\"- 阈值越低：匹配越宽松，结果越多但可能包含误报\")\n", "print(\"- 建议根据实际应用调整阈值\")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已将相似像素转换为多边形\n", "- 矢量化尺度：10米\n", "- 连接方式：4连通\n", "- 每个多边形代表一个匹配的站点\n"]}], "source": ["# 将结果转换为多边形\n", "# 使用selfMask()掩膜0值，只获取匹配像素的多边形\n", "polygons = similar_pixels.selfMask().reduceToVectors(**{\n", "    'scale': scale,\n", "    'eightConnected': <PERSON><PERSON><PERSON>,\n", "    'maxPixels': 1e10,\n", "    'geometry': geometry\n", "})\n", "\n", "print(\"已将相似像素转换为多边形\")\n", "print(f\"- 矢量化尺度：{scale}米\")\n", "print(\"- 连接方式：4连通\")\n", "print(\"- 每个多边形代表一个匹配的站点\")"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已提取匹配位置的质心\n", "- 预测匹配数量：20\n", "- 每个点代表一个潜在的谷仓位置\n", "- 质心提供了匹配区域的中心位置\n"]}], "source": ["# 提取矢量化多边形的质心\n", "predicted_matches = polygons.map(lambda f: f.centroid(**{'maxError': 1}))\n", "\n", "print(\"已提取匹配位置的质心\")\n", "print(f\"- 预测匹配数量：{predicted_matches.size().getInfo()}\")\n", "print(\"- 每个点代表一个潜在的谷仓位置\")\n", "print(\"- 质心提供了匹配区域的中心位置\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化匹配结果\n", "\n", "将预测的匹配位置添加到地图中进行可视化。"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已添加预测匹配位置到地图\n", "- 青色点：预测的谷仓位置\n", "- 黄色点：原始参考位置\n", "- 红色边界：搜索区域\n"]}], "source": ["# 添加预测匹配到地图\n", "Map.addLayer(predicted_matches, {'color': 'cyan'}, 'Predicted Matches')\n", "\n", "print(\"已添加预测匹配位置到地图\")\n", "print(\"- 青色点：预测的谷仓位置\")\n", "print(\"- 黄色点：原始参考位置\")\n", "print(\"- 红色边界：搜索区域\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 验证结果\n", "\n", "评估相似性搜索的结果质量。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["结果统计：\n", "- 总匹配数量：3\n"]}, {"ename": "EEException", "evalue": "Geometry.area: Unable to perform this geometry operation. Please specify a non-zero error margin.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mHttpError\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:408\u001b[39m, in \u001b[36m_execute_cloud_call\u001b[39m\u001b[34m(call, num_retries)\u001b[39m\n\u001b[32m    407\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m408\u001b[39m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcall\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    409\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m googleapiclient.errors.HttpError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\googleapiclient\\_helpers.py:130\u001b[39m, in \u001b[36mpositional.<locals>.positional_decorator.<locals>.positional_wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    129\u001b[39m         logger.warning(message)\n\u001b[32m--> \u001b[39m\u001b[32m130\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\googleapiclient\\http.py:938\u001b[39m, in \u001b[36mHttpRequest.execute\u001b[39m\u001b[34m(self, http, num_retries)\u001b[39m\n\u001b[32m    937\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m resp.status >= \u001b[32m300\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m938\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m HttpError(resp, content, uri=\u001b[38;5;28mself\u001b[39m.uri)\n\u001b[32m    939\u001b[39m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.postproc(resp, content)\n", "\u001b[31mHttpError\u001b[39m: <HttpError 400 when requesting https://earthengine.googleapis.com/v1/projects/825775132399/value:compute?prettyPrint=false&alt=json returned \"Geometry.area: Unable to perform this geometry operation. Please specify a non-zero error margin.\". Details: \"Geometry.area: Unable to perform this geometry operation. Please specify a non-zero error margin.\">", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mEEException\u001b[39m                               Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 7\u001b[39m\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m结果统计：\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m- 总匹配数量：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnum_matches.getInfo()\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m- 搜索区域面积：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43msearch_area_km2\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetInfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 平方公里\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      8\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m- 匹配密度：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m(num_matches.divide(search_area_km2)).getInfo()\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 个/平方公里\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     10\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m结果评估建议：\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\computedobject.py:107\u001b[39m, in \u001b[36mComputedObject.getInfo\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    101\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgetInfo\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> Optional[Any]:\n\u001b[32m    102\u001b[39m \u001b[38;5;250m  \u001b[39m\u001b[33;03m\"\"\"Fetch and return information about this object.\u001b[39;00m\n\u001b[32m    103\u001b[39m \n\u001b[32m    104\u001b[39m \u001b[33;03m  Returns:\u001b[39;00m\n\u001b[32m    105\u001b[39m \u001b[33;03m    The object can evaluate to anything.\u001b[39;00m\n\u001b[32m    106\u001b[39m \u001b[33;03m  \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m107\u001b[39m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcomputeValue\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:1128\u001b[39m, in \u001b[36mcomputeValue\u001b[39m\u001b[34m(obj)\u001b[39m\n\u001b[32m   1125\u001b[39m body = {\u001b[33m'\u001b[39m\u001b[33mexpression\u001b[39m\u001b[33m'\u001b[39m: serializer.encode(obj, for_cloud_api=\u001b[38;5;28;01mTrue\u001b[39;00m)}\n\u001b[32m   1126\u001b[39m _maybe_populate_workload_tag(body)\n\u001b[32m-> \u001b[39m\u001b[32m1128\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_execute_cloud_call\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1129\u001b[39m \u001b[43m    \u001b[49m\u001b[43m_get_cloud_projects\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1130\u001b[39m \u001b[43m    \u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1131\u001b[39m \u001b[43m    \u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproject\u001b[49m\u001b[43m=\u001b[49m\u001b[43m_get_projects_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprettyPrint\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m   1132\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m[\u001b[33m'\u001b[39m\u001b[33mresult\u001b[39m\u001b[33m'\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32mf:\\Anaconda\\envs\\gee\\Lib\\site-packages\\ee\\data.py:410\u001b[39m, in \u001b[36m_execute_cloud_call\u001b[39m\u001b[34m(call, num_retries)\u001b[39m\n\u001b[32m    408\u001b[39m   \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m call.execute(num_retries=num_retries)\n\u001b[32m    409\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m googleapiclient.errors.HttpError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m410\u001b[39m   \u001b[38;5;28;01mraise\u001b[39;00m _translate_cloud_exception(e)\n", "\u001b[31mEEException\u001b[39m: Geometry.area: Unable to perform this geometry operation. Please specify a non-zero error margin."]}], "source": ["# 计算匹配统计信息\n", "num_matches = predicted_matches.size()\n", "search_area_km2 = geometry.area().divide(1000000)  # 转换为平方公里\n", "\n", "print(\"结果统计：\")\n", "print(f\"- 总匹配数量：{num_matches.getInfo()}\")\n", "print(f\"- 搜索区域面积：{search_area_km2.getInfo():.1f} 平方公里\")\n", "print(f\"- 匹配密度：{(num_matches.divide(search_area_km2)).getInfo():.2f} 个/平方公里\")\n", "\n", "print(\"\\n结果评估建议：\")\n", "print(\"1. 放大每个预测匹配位置，检查是否确实有谷仓\")\n", "print(\"2. 观察匹配位置是否与参考位置有相似特征\")\n", "print(\"3. 如果误报较多，可以提高阈值\")\n", "print(\"4. 如果漏检较多，可以降低阈值\")\n", "print(\"5. 可以添加更多参考位置来改善结果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 结果分析和改进建议\n", "\n", "分析相似性搜索的性能和改进方向。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["相似性搜索结果分析：\n", "\n", "✓ 正确匹配的特征：\n", "- 许多匹配项正确识别了谷仓\n", "- 匹配位置与参考位置有相似的形状和结构\n", "- 通常由相似材料制成，具有独特的嵌入向量\n", "\n", "⚠ 误报的可能原因：\n", "- 其他具有相似特征的建筑结构\n", "- 相似的光谱和空间特征\n", "- 阈值设置可能需要调整\n", "\n", "🔧 改进建议：\n", "1. 调整参考位置：选择更具代表性的谷仓样本\n", "2. 优化阈值：根据实际结果调整相似度阈值\n", "3. 多尺度分析：尝试不同的采样尺度\n", "4. 后处理：应用形态学操作去除噪声\n", "5. 验证数据：收集实地数据进行精度评估\n"]}], "source": ["print(\"相似性搜索结果分析：\")\n", "print(\"\\n✓ 正确匹配的特征：\")\n", "print(\"- 许多匹配项正确识别了谷仓\")\n", "print(\"- 匹配位置与参考位置有相似的形状和结构\")\n", "print(\"- 通常由相似材料制成，具有独特的嵌入向量\")\n", "\n", "print(\"\\n⚠ 误报的可能原因：\")\n", "print(\"- 其他具有相似特征的建筑结构\")\n", "print(\"- 相似的光谱和空间特征\")\n", "print(\"- 阈值设置可能需要调整\")\n", "\n", "print(\"\\n🔧 改进建议：\")\n", "print(\"1. 调整参考位置：选择更具代表性的谷仓样本\")\n", "print(\"2. 优化阈值：根据实际结果调整相似度阈值\")\n", "print(\"3. 多尺度分析：尝试不同的采样尺度\")\n", "print(\"4. 后处理：应用形态学操作去除噪声\")\n", "print(\"5. 验证数据：收集实地数据进行精度评估\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 显示交互式地图\n", "\n", "地图包含以下图层，可以切换查看不同结果："]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图图层说明：\n", "1. Search Area - 搜索区域边界（红色）\n", "2. Reference Locations - 参考位置（黄色点）\n", "3. Similarity (bright = close) - 相似度热图\n", "4. Predicted Matches - 预测匹配位置（青色点）\n", "\n", "使用说明：\n", "- 可以切换图层查看不同信息\n", "- 放大查看具体匹配位置的详细情况\n", "- 对比参考位置和匹配位置的特征\n", "- 使用卫星底图验证结果的准确性\n", "\n", "可以在地图界面中进行交互式探索和验证\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aa2fd3e31d82415fbcd6f069df0b36b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=401410.0, center=[38.902255277197845, 110.5162811279297], controls=(WidgetControl(options=['positio…"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"地图图层说明：\")\n", "print(\"1. Search Area - 搜索区域边界（红色）\")\n", "print(\"2. Reference Locations - 参考位置（黄色点）\")\n", "print(\"3. Similarity (bright = close) - 相似度热图\")\n", "print(\"4. Predicted Matches - 预测匹配位置（青色点）\")\n", "print(\"\\n使用说明：\")\n", "print(\"- 可以切换图层查看不同信息\")\n", "print(\"- 放大查看具体匹配位置的详细情况\")\n", "print(\"- 对比参考位置和匹配位置的特征\")\n", "print(\"- 使用卫星底图验证结果的准确性\")\n", "print(\"\\n可以在地图界面中进行交互式探索和验证\")\n", "\n", "# 显示地图\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "本教程展示了如何使用卫星嵌入数据集进行相似性搜索：\n", "\n", "### 主要步骤\n", "1. **区域选择** - 选择了堪萨斯州富兰克林县作为搜索区域\n", "2. **参考位置** - 标记了几个谷仓位置作为参考\n", "3. **嵌入提取** - 从参考位置提取64维嵌入向量\n", "4. **相似度计算** - 使用点积计算相似度\n", "5. **阈值应用** - 设置阈值提取匹配位置\n", "6. **结果验证** - 分析和评估搜索结果\n", "\n", "### 技术优势\n", "- **无需训练** - 不需要训练复杂的对象检测模型\n", "- **少样本学习** - 只需几个参考样本即可搜索\n", "- **语义理解** - 嵌入向量包含丰富的语义信息\n", "- **可扩展性** - 方法可应用于其他对象类型\n", "\n", "### 应用场景\n", "- 农业设施检测（谷仓、温室等）\n", "- 基础设施监测（建筑、道路等）\n", "- 环境监测（特定地貌、植被类型等）\n", "- 灾害评估（损坏建筑、变化检测等）\n", "\n", "### 改进方向\n", "- 优化参考样本选择\n", "- 调整相似度阈值\n", "- 结合多尺度分析\n", "- 应用后处理技术\n", "- 集成验证数据"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}