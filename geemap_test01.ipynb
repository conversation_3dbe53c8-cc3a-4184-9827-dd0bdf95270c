{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b9240242-8c73-45c8-ac34-3023f8cf0da1", "metadata": {}, "outputs": [], "source": ["import geemap\n", "import ee\n", "geemap.set_proxy(port=33210)\n", "geemap.ee_initialize()"]}, {"cell_type": "code", "execution_count": 4, "id": "952a3c63-265c-477e-8de2-83d6abda5f70", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'type': 'Image',\n", " 'bands': [{'id': 'elevation',\n", "   'data_type': {'type': 'PixelType',\n", "    'precision': 'int',\n", "    'min': -32768,\n", "    'max': 32767},\n", "   'dimensions': [1296001, 417601],\n", "   'crs': 'EPSG:4326',\n", "   'crs_transform': [0.0002777777777777778,\n", "    0,\n", "    -180.0001388888889,\n", "    0,\n", "    -0.0002777777777777778,\n", "    60.00013888888889]}],\n", " 'version': 1641990767055141,\n", " 'id': 'USGS/SRTMGL1_003',\n", " 'properties': {'system:visualization_0_min': '0.0',\n", "  'type_name': 'Image',\n", "  'keywords': ['dem',\n", "   'elevation',\n", "   'geophysical',\n", "   'nasa',\n", "   'srtm',\n", "   'topography',\n", "   'usgs'],\n", "  'thumb': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_thumb.png',\n", "  'description': '<p>The Shuttle Radar Topography Mission (SRTM, see <a href=\"https://onlinelibrary.wiley.com/doi/10.1029/2005RG000183/full\">Farr\\net al. 2007</a>)\\ndigital elevation data is an international research effort that\\nobtained digital elevation models on a near-global scale. This\\nSRTM V3 product (SRTM Plus) is provided by NASA JPL\\nat a resolution of 1 arc-second (approximately 30m).</p><p>This dataset has undergone a void-filling process using open-source data\\n(ASTER GDEM2, GMTED2010, and NED), as opposed to other versions that\\ncontain voids or have been void-filled with commercial sources.\\nFor more information on the different versions see the\\n<a href=\"https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf\">SRTM Quick Guide</a>.</p><p>Documentation:</p><ul><li><p><a href=\"https://lpdaac.usgs.gov/documents/179/SRTM_User_Guide_V3.pdf\">User&#39;s Guide</a></p></li><li><p><a href=\"https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf\">General Documentation</a></p></li><li><p><a href=\"https://doi.org/10.1029/2005RG000183\">Algorithm Theoretical Basis Document (ATBD)</a></p></li></ul><p><b>Provider: <a href=\"https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html\">NASA / USGS / JPL-Caltech</a></b><br><p><b>Bands</b><table class=\"eecat\"><tr><th scope=\"col\">Name</th><th scope=\"col\">Description</th></tr><tr><td>elevation</td><td><p>Elevation</p></td></tr></table><p><b>Terms of Use</b><br><p>Unless otherwise noted, images and video on JPL public\\nweb sites (public sites ending with a jpl.nasa.gov address) may\\nbe used for any purpose without prior permission. For more information\\nand exceptions visit the <a href=\"https://www.jpl.nasa.gov/imagepolicy/\">JPL Image Use Policy site</a>.</p><p><b>Suggested citation(s)</b><ul><li><p>Farr, T.G., Rosen, P.A., Caro, E., Crippen, R., Duren, R., Hensley,\\nS., Kobrick, M., Paller, M., Rodriguez, E., Roth, L., Seal, D.,\\nShaffer, S., Shimada, J., Umland, J., Werner, M., Oskin, M., Burbank,\\nD., and Alsdorf, D.E., 2007, The shuttle radar topography mission:\\nReviews of Geophysics, v. 45, no. 2, RG2004, at\\n<a href=\"https://doi.org/10.1029/2005RG000183\">https://doi.org/10.1029/2005RG000183</a>.</p></li></ul><style>\\n  table.eecat {\\n  border: 1px solid black;\\n  border-collapse: collapse;\\n  font-size: 13px;\\n  }\\n  table.eecat td, tr, th {\\n  text-align: left; vertical-align: top;\\n  border: 1px solid gray; padding: 3px;\\n  }\\n  td.nobreak { white-space: nowrap; }\\n</style>',\n", "  'source_tags': ['nasa', 'usgs'],\n", "  'visualization_0_max': '6000.0',\n", "  'title': 'NASA SRTM Digital Elevation 30m',\n", "  'product_tags': ['srtm', 'elevation', 'topography', 'dem', 'geophysical'],\n", "  'provider': 'NASA / USGS / JPL-Caltech',\n", "  'visualization_0_min': '0.0',\n", "  'visualization_0_name': 'Elevation',\n", "  'date_range': [************, ************],\n", "  'system:visualization_0_gamma': '1.6',\n", "  'period': 0,\n", "  'system:visualization_0_bands': 'elevation',\n", "  'provider_url': 'https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html',\n", "  'visualization_0_gamma': '1.6',\n", "  'sample': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_sample.png',\n", "  'tags': ['dem',\n", "   'elevation',\n", "   'geophysical',\n", "   'nasa',\n", "   'srtm',\n", "   'topography',\n", "   'usgs'],\n", "  'system:visualization_0_max': '6000.0',\n", "  'system:visualization_0_name': 'Elevation',\n", "  'system:asset_size': 132792638252,\n", "  'visualization_0_bands': 'elevation'}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 在geemap中，需要用到getInfo()函数读取数据信息，这与GEE利用print函数读取有所不同\n", "image = ee.Image(\"USGS/SRTMGL1_003\")\n", "image.getInfo()"]}, {"cell_type": "code", "execution_count": 5, "id": "93fbf6ae-71a2-487e-8072-386f3f964604", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c12388207d0e43519afa360ec2a35cc5", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[0, 0], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=SearchDataGUI(childr…"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Map=geemap.Map(height=400)\n", "img = ee.Image(\"USGS/SRTMGL1_003\")\n", "vis_params = {\n", "    'min': 0,\n", "    'max': 6000,\n", "    'palette': ['006633', 'E5FFCC', '662A00', 'D8D8D8', 'F5F5F5'],\n", "}\n", "Map.addLayer(img,vis_params,'img')\n", "Map"]}, {"cell_type": "code", "execution_count": 6, "id": "c0b09bf6-6bf8-44dd-9535-32137404ee99", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c12388207d0e43519afa360ec2a35cc5", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=1079.0, center=[17.7009, 83.277], controls=(WidgetControl(options=['position', 'transparent_bg'], w…"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#数据集合的筛选\n", "collection = (\n", "    ee.ImageCollection('COPERNICUS/S2_SR')\n", "    .filterDate('2021-01-01', '2022-01-01')\n", "    .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 5))\n", ")\n", "#获取影像集合的数据信息\n", "collection.first().getInfo()\n", "#获取影像集合的数量信息\n", "collection.size().getInfo()\n", "#数据集合的可视化\n", "image = collection.median()\n", "vis = {\n", "    'min': 0.0,\n", "    'max': 3000,\n", "    'bands': ['B4', 'B3', 'B2'],\n", "}\n", "Map.setCenter(83.277, 17.7009, 12)\n", "Map.addLayer(image, vis, 'Sentinel-2')\n", "Map"]}, {"cell_type": "code", "execution_count": 9, "id": "7512ccec-bce1-436b-8298-d819781b69f6", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c12388207d0e43519afa360ec2a35cc5", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=472179.0, center=[17.671829516248756, 83.30795288085939], controls=(WidgetControl(options=['positio…"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["roi = Map.user_roi\n", "image_clip = image.clip(roi)\n", "Map.center_object(roi)\n", "Map.addLayer(image_clip, vis, 'image_clip')\n", "Map"]}, {"cell_type": "code", "execution_count": 13, "id": "837baeb0-5b17-4d83-91d6-035daf6ced0b", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c12388207d0e43519afa360ec2a35cc5", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=1834.0, center=[34.161818161230386, 107.49023437500001], controls=(WidgetControl(options=['position…"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["path = r'E:\\Heritage\\FloodArea\\lyy\\1\\area1.shp'\n", "fc = geemap.shp_to_ee(path)\n", "image_beijing = image.clipToCollection(fc)\n", "Map.centerObject(fc)\n", "Map.addLayer(image_beijing, vis, 'image_beijing')\n", "Map"]}, {"cell_type": "code", "execution_count": 14, "id": "55b3154d-b791-4bed-9df2-924949b2cab6", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Generating URL ...\n", "An error occurred while downloading.\n", "Pixel grid dimensions (126308x118741) must be less than or equal to 32768.\n"]}], "source": ["geemap.ee_export_image(image_beijing, filename=\"image_beijing.tif\", scale=10, region=fc.first().geometry())"]}, {"cell_type": "code", "execution_count": null, "id": "4c68b89c-fbbd-4408-9e6e-6c188efa8aa6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}