# 卫星嵌入数据集简介 - Python版本

本项目将Google Earth Engine的JavaScript教程"卫星嵌入数据集简介"转换为Python代码。

## 原始教程链接
https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-01-introduction?hl=zh-cn

## 文件说明

1. **satellite_embedding_introduction.py** - 完整的Python脚本版本
2. **satellite_embedding_introduction.ipynb** - Jupyter Notebook版本（推荐）
3. **README.md** - 本说明文档

## 环境要求

在运行代码之前，请确保安装了以下Python包：

```bash
# 使用conda安装（推荐）
conda install -c conda-forge earthengine-api
conda install -c conda-forge geemap

# 或使用pip安装
pip install earthengine-api
pip install geemap
```

## 使用方法

### 1. 认证Earth Engine

首次使用需要进行Earth Engine认证：

```python
import ee
ee.Authenticate()  # 这会打开浏览器进行认证
ee.Initialize()
```

### 2. 运行代码

#### 方法一：使用Jupyter Notebook（推荐）
```bash
jupyter notebook satellite_embedding_introduction.ipynb
```

#### 方法二：直接运行Python脚本
```bash
python satellite_embedding_introduction.py
```

## 教程内容

本教程演示了以下内容：

### 1. 卫星嵌入数据集简介
- 什么是嵌入（Embedding）
- AlphaEarth Foundations模型的工作原理
- 64维嵌入向量的含义

### 2. 数据访问和处理
- 访问Google的卫星嵌入数据集
- 时间和空间过滤
- 图像镶嵌处理

### 3. 数据可视化
- RGB可视化嵌入数据的三个维度
- 交互式地图显示

### 4. 无监督聚类分析
- 使用K-means聚类算法
- 生成3、5、10个聚类的结果
- 分析不同聚类数量的效果

## 主要代码转换对比

### JavaScript原版 → Python版本

#### 1. 基础设置
```javascript
// JavaScript
var embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL');
Map.setOptions('SATELLITE');
```

```python
# Python
embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')
Map = geemap.Map()
Map.add_basemap('SATELLITE')
```

#### 2. 几何定义
```javascript
// JavaScript
var geometry = ee.Geometry.Polygon([[
  [76.3978, 12.5521],
  [76.3978, 12.3550],
  [76.6519, 12.3550],
  [76.6519, 12.5521]
]]);
```

```python
# Python
geometry = ee.Geometry.Polygon([[
    [76.3978, 12.5521],
    [76.3978, 12.3550],
    [76.6519, 12.3550],
    [76.6519, 12.5521]
]])
```

#### 3. 数据过滤
```javascript
// JavaScript
var filteredEmbeddings = embeddings
  .filter(ee.Filter.date(startDate, endDate))
  .filter(ee.Filter.bounds(geometry));
```

```python
# Python
filtered_embeddings = embeddings \
    .filter(ee.Filter.date(start_date, end_date)) \
    .filter(ee.Filter.bounds(geometry))
```

#### 4. 聚类函数
```javascript
// JavaScript
var getClusters = function(nClusters) {
  var clusterer = ee.Clusterer.wekaKMeans({nClusters: nClusters})
    .train(training);
  var clustered = embeddingsImage.cluster(clusterer);
  return clustered;
};
```

```python
# Python
def get_clusters(n_clusters):
    clusterer = ee.Clusterer.wekaKMeans(n_clusters).train(training)
    clustered = embeddings_image.cluster(clusterer)
    return clustered
```

## 预期结果

运行代码后，您将看到：

1. **嵌入图像可视化** - 使用3个波段（A01, A16, A09）的RGB组合
2. **3聚类结果** - 显示主要土地覆盖类型
3. **5聚类结果** - 更细致的分类
4. **10聚类结果** - 详细的土地利用模式，包括不同作物类型

## 注意事项

1. 确保有稳定的网络连接，因为需要访问Google Earth Engine服务
2. 首次运行可能需要较长时间来下载和处理数据
3. 如果遇到认证问题，请检查Earth Engine账户权限
4. 建议在Jupyter Notebook环境中运行以获得最佳交互体验

## 扩展学习

本教程是卫星嵌入数据集系列教程的第一部分，后续教程包括：
- 无监督分类
- 监督式分类  
- 回归分析
- 相似性搜索

## 技术支持

如果遇到问题，请参考：
- [Google Earth Engine Python API文档](https://developers.google.com/earth-engine/guides/python_install)
- [geemap文档](https://geemap.org/)
- [Earth Engine社区论坛](https://groups.google.com/forum/#!forum/google-earth-engine-developers)
