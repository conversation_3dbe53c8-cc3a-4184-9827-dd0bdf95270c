{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 测试下载Satellite Embedding数据\n", "\n", "## 目标\n", "- 测试下载功能\n", "- 下载2022年的前3个波段作为测试\n", "- 验证数据完整性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import os\n", "from pathlib import Path\n", "import time\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"开始测试下载功能\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置测试参数\n", "print(\"=== 设置测试参数 ===\")\n", "\n", "# 测试路径\n", "test_path = r\"H:\\satellite_embedding\\test\"\n", "\n", "# 测试年份和波段\n", "test_year = 2022\n", "test_bands = ['A00', 'A01', 'A02']  # 只测试前3个波段\n", "\n", "# 研究区域：较小的测试区域\n", "roi = ee.Geometry.Polygon([[\n", "    [80, 35],   # 西南角\n", "    [80, 40],   # 西北角\n", "    [90, 40],   # 东北角\n", "    [90, 35]    # 东南角\n", "]])\n", "\n", "# 下载参数\n", "crs = \"EPSG:4326\"\n", "scale = 27830  # 约0.25度分辨率\n", "\n", "print(f\"测试路径: {test_path}\")\n", "print(f\"测试年份: {test_year}\")\n", "print(f\"测试波段: {test_bands}\")\n", "print(f\"测试区域: 80°-90°E, 35°-40°N\")\n", "print(f\"分辨率: 0.25° (scale={scale})\")\n", "\n", "# 创建测试目录\n", "Path(test_path).mkdir(parents=True, exist_ok=True)\n", "print(f\"已创建测试目录: {test_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试下载单个波段\n", "print(\"\\n=== 测试下载功能 ===\")\n", "\n", "def test_download_band(year, band, roi, output_path, crs, scale):\n", "    \"\"\"\n", "    测试下载指定年份和波段的satellite embedding数据\n", "    \"\"\"\n", "    try:\n", "        print(f\"  正在下载 {year}年 {band}波段...\")\n", "        \n", "        # 加载satellite embedding数据集\n", "        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "        \n", "        # 设置时间范围\n", "        start_date = ee.Date.fromYMD(year, 1, 1)\n", "        end_date = start_date.advance(1, 'year')\n", "        \n", "        # 过滤数据\n", "        filtered = embeddings \\\n", "            .filter(ee.Filter.date(start_date, end_date)) \\\n", "            .filter(ee.Filter.bounds(roi))\n", "        \n", "        # 创建镶嵌图并选择指定波段\n", "        image = filtered.mosaic().select([band])\n", "        \n", "        print(f\"    数据准备完成，开始下载...\")\n", "        \n", "        # 下载图像\n", "        geemap.download_ee_image(\n", "            image=image,\n", "            filename=output_path,\n", "            region=roi,\n", "            crs=crs,\n", "            scale=scale,\n", "        )\n", "        \n", "        # 检查文件是否存在\n", "        if os.path.exists(output_path):\n", "            file_size = os.path.getsize(output_path)\n", "            print(f\"    ✓ 下载成功: {output_path}\")\n", "            print(f\"    文件大小: {file_size/1024:.1f} KB\")\n", "            return True, None\n", "        else:\n", "            return False, \"文件未创建\"\n", "        \n", "    except Exception as e:\n", "        return False, str(e)\n", "\n", "# 执行测试下载\n", "success_count = 0\n", "total_count = len(test_bands)\n", "\n", "for band in test_bands:\n", "    filename = f\"{band}_{test_year}_test.tif\"\n", "    output_path = os.path.join(test_path, filename)\n", "    \n", "    success, error = test_download_band(\n", "        test_year, band, roi, output_path, crs, scale\n", "    )\n", "    \n", "    if success:\n", "        success_count += 1\n", "    else:\n", "        print(f\"    ✗ 下载失败: {error}\")\n", "    \n", "    print()  # 空行分隔\n", "\n", "print(f\"测试完成: {success_count}/{total_count} 个文件下载成功\")\n", "\n", "if success_count == total_count:\n", "    print(\"\\n🎉 测试通过！可以开始批量下载\")\n", "else:\n", "    print(\"\\n⚠️ 测试部分失败，请检查网络和参数设置\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}