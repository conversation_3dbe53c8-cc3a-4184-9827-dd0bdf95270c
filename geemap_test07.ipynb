{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8e34a945", "metadata": {}, "outputs": [], "source": ["import geemap\n", "import ee\n", "geemap.set_proxy(port=33210)\n", "geemap.ee_initialize()\n", "Map=geemap.Map(height=400)"]}, {"cell_type": "code", "execution_count": null, "id": "51cc3403", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bd4d4819", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23627132", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "38346fbb", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e07495343a24645af86edab83c5cbdc", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[0, 0], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=SearchDataGUI(childr…"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}