{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 使用卫星嵌入数据集进行监督分类 - Python版本\n", "\n", "基于Google Earth Engine教程转换而来\n", "https://developers.google.cn/earth-engine/tutorials/community/satellite-embedding-03-supervised-classification?hl=zh-cn\n", "\n", "本教程演示如何：\n", "1. 使用卫星嵌入数据集进行监督分类\n", "2. 收集和标记训练样本\n", "3. 训练k-最近邻(kNN)分类器\n", "4. 对红树林进行分类和制图\n", "5. 验证分类结果\n", "\n", "## 背景介绍\n", "\n", "卫星嵌入可用于标准遥感分类工作流。这些嵌入专门设计用于在少样本学习方面表现出色，这意味着只需相对较少的已标记数据（例如数十到数百个样本）即可实现高质量的分类结果。由于嵌入包含光谱、空间和时间背景信息，因此简单的分类器（例如k-Nearest Neighbors (kNN)或Random Forest）可以使用嵌入向量将复杂的景观分类为目标类别。\n", "\n", "在本教程中，我们将学习如何使用kNN分类器通过卫星嵌入来对红树林进行分类，从而采用监督式学习方法。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Earth Engine已初始化\n"]}], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图已创建\n"]}], "source": ["# 创建交互式地图\n", "Map = geemap.Map()\n", "\n", "print(\"地图已创建\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 选择区域\n", "\n", "我们选择肯尼亚海岸线沿线的一个区域作为研究区域，该区域包含红树林生态系统。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已选择肯尼亚海岸线区域作为研究区域\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1fb8670cdeca466dabe5f2154ed80f0c", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[0, 0], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=SearchDataGUI(childr…"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义感兴趣的区域 - 肯尼亚海岸线\n", "geometry = ee.Geometry.Polygon([[\n", "    [39.4926, -4.39833],\n", "    [39.4926, -4.47394],\n", "    [39.5491, -4.47394],\n", "    [39.5491, -4.39833]\n", "]])\n", "\n", "# 将地图中心设置到选定区域\n", "Map.centerObject(geometry)\n", "Map.addLayer(geometry, {'color': 'red'}, 'Study Area', False)\n", "\n", "print(\"已选择肯尼亚海岸线区域作为研究区域\")\n", "\n", "Map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 创建参考影像\n", "\n", "为了能够正确标记训练样本，我们首先创建一个Sentinel-2无云合成影像。我们选择了一种假彩色可视化效果，可突出显示水、植被和建筑表面之间的差异。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["分类年份：2020\n", "时间范围：2020-01-01 到 2021-01-01\n"]}], "source": ["# 选择分类年份\n", "year = 2020\n", "start_date = ee.Date.fromYMD(year, 1, 1)\n", "end_date = start_date.advance(1, 'year')\n", "\n", "print(f\"分类年份：{year}\")\n", "print(f\"时间范围：{year}-01-01 到 {year+1}-01-01\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已创建Sentinel-2假彩色合成影像\n", "- 红色：SWIR波段(B11)\n", "- 绿色：近红外波段(B8)\n", "- 蓝色：红色波段(B4)\n"]}], "source": ["# 创建Sentinel-2合成影像用于选择训练样本\n", "s2 = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')\n", "filtered_s2 = s2 \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "# 使用Cloud Score+进行云掩膜\n", "cs_plus = ee.ImageCollection('GOOGLE/CLOUD_SCORE_PLUS/V1/S2_HARMONIZED')\n", "cs_plus_bands = cs_plus.first().bandNames()\n", "filtered_s2_with_cs = filtered_s2.linkCollection(cs_plus, cs_plus_bands)\n", "\n", "def mask_low_qa(image):\n", "    qa_band = 'cs'\n", "    clear_threshold = 0.6\n", "    mask = image.select(qa_band).gte(clear_threshold)\n", "    return image.updateMask(mask)\n", "\n", "filtered_s2_masked = filtered_s2_with_cs \\\n", "    .map(mask_low_qa) \\\n", "    .select('B.*')\n", "\n", "# 创建云掩膜影像的中值合成\n", "composite = filtered_s2_masked.median()\n", "\n", "# 显示输入合成影像\n", "swir_vis = {'min': 300, 'max': 4000, 'bands': ['B11', 'B8', 'B4']}\n", "Map.addLayer(composite.clip(geometry), swir_vis, 'S2 Composite (False Color)')\n", "\n", "print(\"已创建Sentinel-2假彩色合成影像\")\n", "print(\"- 红色：SWIR波段(B11)\")\n", "print(\"- 绿色：近红外波段(B8)\")\n", "print(\"- 蓝色：红色波段(B4)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 收集训练样本\n", "\n", "使用卫星嵌入的分类工作流只需少量带标签的样本即可获得相对准确的结果。我们将创建一个3类分类：\n", "\n", "| 土地覆盖类别 | 说明 | 类别值 |\n", "|-------------|------|--------|\n", "| 红树林 | 所有耐盐的沿海植被物种 | 1 |\n", "| 水 | 所有地表水 - 湖泊、池塘、河流、海洋等 | 2 |\n", "| 其他 | 所有其他地表 - 包括建筑、裸露的土壤、沙地、农作物、树木等 | 3 |\n", "\n", "**注意：** 在实际应用中，您需要使用geemap的交互式工具或预先准备的训练样本。这里我们创建一些示例训练点。"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已创建训练样本：\n", "- 红树林样本：5个\n", "- 水体样本：5个\n", "- 其他类别样本：5个\n", "- 总样本数：15个\n"]}], "source": ["# 创建训练样本\n", "# 注意：在实际应用中，您应该使用geemap的交互式工具收集训练样本\n", "# 这里我们创建一些示例训练点\n", "\n", "# 红树林样本点\n", "mangrove_points = [\n", "    [39.5200, -4.4200],\n", "    [39.5150, -4.4250],\n", "    [39.5100, -4.4300],\n", "    [39.5050, -4.4350],\n", "    [39.5000, -4.4400]\n", "]\n", "\n", "# 水体样本点\n", "water_points = [\n", "    [39.5300, -4.4100],\n", "    [39.5350, -4.4150],\n", "    [39.5400, -4.4200],\n", "    [39.5450, -4.4250],\n", "    [39.5500, -4.4300]\n", "]\n", "\n", "# 其他类别样本点\n", "other_points = [\n", "    [39.5000, -4.4000],\n", "    [39.5050, -4.4050],\n", "    [39.5100, -4.4100],\n", "    [39.5150, -4.4150],\n", "    [39.5200, -4.4500]\n", "]\n", "\n", "# 创建FeatureCollection\n", "def create_feature_collection(points, landcover_value):\n", "    features = []\n", "    for point in points:\n", "        feature = ee.Feature(\n", "            ee.Geometry.Point(point),\n", "            {'landcover': landcover_value}\n", "        )\n", "        features.append(feature)\n", "    return ee.FeatureCollection(features)\n", "\n", "mangroves = create_feature_collection(mangrove_points, 1)\n", "water = create_feature_collection(water_points, 2)\n", "other = create_feature_collection(other_points, 3)\n", "\n", "# 合并所有训练样本\n", "gcps = mangroves.merge(water).merge(other)\n", "\n", "# 添加训练样本到地图\n", "Map.addLayer(mangroves, {'color': 'green'}, 'Mangrove Samples', False)\n", "Map.addLayer(water, {'color': 'blue'}, 'Water Samples', False)\n", "Map.addLayer(other, {'color': 'gray'}, 'Other Samples', False)\n", "\n", "print(\"已创建训练样本：\")\n", "print(f\"- 红树林样本：{len(mangrove_points)}个\")\n", "print(f\"- 水体样本：{len(water_points)}个\")\n", "print(f\"- 其他类别样本：{len(other_points)}个\")\n", "print(f\"- 总样本数：{len(mangrove_points) + len(water_points) + len(other_points)}个\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 准备卫星嵌入数据集\n", "\n", "加载卫星嵌入数据集，过滤出所选年份和感兴趣区域的图块，创建镶嵌图。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已加载和处理卫星嵌入数据集\n", "- 时间范围：2020年\n", "- 已创建嵌入数据镶嵌图\n", "- 波段数量：64个（A00到A63）\n"]}], "source": ["# 加载卫星嵌入数据集\n", "embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "\n", "embeddings_filtered = embeddings \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "embeddings_image = embeddings_filtered.mosaic()\n", "\n", "print(\"已加载和处理卫星嵌入数据集\")\n", "print(f\"- 时间范围：{year}年\")\n", "print(\"- 已创建嵌入数据镶嵌图\")\n", "print(\"- 波段数量：64个（A00到A63）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练分类器\n", "\n", "对嵌入向量进行抽样以创建训练数据集，然后训练k-最近邻(kNN)分类器。"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["训练数据提取完成\n", "- 训练样本数量：15\n", "- 特征维度：64维嵌入向量\n", "- 类别属性：landcover\n", "\n", "第一个训练样本的属性：\n"]}], "source": ["# 在样本位置对嵌入图像进行采样以获取训练数据\n", "training = embeddings_image.sampleRegions(**{\n", "    'collection': gcps,\n", "    'properties': ['landcover'],\n", "    'scale': 10\n", "})\n", "\n", "print(\"训练数据提取完成\")\n", "print(f\"- 训练样本数量：{training.size().getInfo()}\")\n", "print(\"- 特征维度：64维嵌入向量\")\n", "print(\"- 类别属性：landcover\")\n", "\n", "# 显示第一个训练特征的信息\n", "first_feature = training.first()\n", "print(\"\\n第一个训练样本的属性：\")\n", "# print(first_feature.getInfo())  # 取消注释以查看详细信息"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["kNN分类器训练完成\n", "- 分类器类型：k-最近邻(kNN)\n", "- 输入特征：64维卫星嵌入向量\n", "- 输出类别：3类（红树林、水体、其他）\n"]}], "source": ["# 训练k-最近邻(kNN)分类器\n", "# 对于少样本分类，kNN是一个不错的选择\n", "classifier = ee.Classifier.smileKNN().train(**{\n", "    'features': training,\n", "    'classProperty': 'landcover',\n", "    'inputProperties': embeddings_image.bandNames()\n", "})\n", "\n", "print(\"kNN分类器训练完成\")\n", "print(\"- 分类器类型：k-最近邻(kNN)\")\n", "print(\"- 输入特征：64维卫星嵌入向量\")\n", "print(\"- 输出类别：3类（红树林、水体、其他）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 执行分类\n", "\n", "使用训练好的分类器来预测卫星嵌入镶嵌图中所有像素的类别。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["分类完成\n", "- 分类结果：每个像素被分配到3个类别之一\n", "- 类别1：红树林\n", "- 类别2：水体\n", "- 类别3：其他\n"]}], "source": ["# 对卫星嵌入镶嵌图进行分类\n", "classified = embeddings_image.classify(classifier)\n", "\n", "print(\"分类完成\")\n", "print(\"- 分类结果：每个像素被分配到3个类别之一\")\n", "print(\"- 类别1：红树林\")\n", "print(\"- 类别2：水体\")\n", "print(\"- 类别3：其他\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化分类结果\n", "\n", "创建分类结果的可视化，并提取红树林地图。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已添加分类结果到地图\n", "图例：\n", "- 绿色：红树林\n", "- 蓝色：水体\n", "- 灰色：其他\n"]}], "source": ["# 选择3色调色板\n", "# 为每个类别分配颜色：红树林、水体、其他\n", "palette = ['green', 'blue', 'gray']\n", "\n", "# 添加分类结果到地图\n", "Map.addLayer(\n", "    classified.clip(geometry),\n", "    {'min': 1, 'max': 3, 'palette': palette},\n", "    'Classified Satellite Embeddings Image'\n", ")\n", "\n", "print(\"已添加分类结果到地图\")\n", "print(\"图例：\")\n", "print(\"- 绿色：红树林\")\n", "print(\"- 蓝色：水体\")\n", "print(\"- 灰色：其他\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已创建红树林分布地图\n", "- 绿色区域：检测到的红树林\n", "- 透明区域：非红树林区域\n"]}], "source": ["# 创建红树林地图\n", "# 提取分类为红树林（类别1）的像素\n", "mangroves_image = classified.eq(1).selfMask()\n", "\n", "mangrove_vis = {'min': 0, 'max': 1, 'palette': ['green']}\n", "\n", "Map.addLayer(\n", "    mangroves_image.clip(geometry),\n", "    mangrove_vis, \n", "    'Mangroves Map (Satellite Embedding Classification)'\n", ")\n", "\n", "print(\"已创建红树林分布地图\")\n", "print(\"- 绿色区域：检测到的红树林\")\n", "print(\"- 透明区域：非红树林区域\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 验证结果\n", "\n", "将我们的结果与高质量的同行评审数据集Global Mangrove Watch进行比较。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["已添加Global Mangrove Watch参考数据\n", "- 数据来源：JAXA L波段SAR\n", "- 时间范围：1996-2020年\n", "- 用途：验证我们的分类结果\n"]}], "source": ["# 加载Global Mangrove Watch数据集进行验证\n", "# 此数据集源自JAXA的L波段合成孔径雷达(SAR)\n", "gmw = ee.ImageCollection(\n", "    'projects/earthengine-legacy/assets/projects/sat-io/open-datasets/GMW/extent/GMW_V3'\n", ")\n", "\n", "gmw_filtered = gmw \\\n", "    .filter(ee.Filter.date(start_date, end_date)) \\\n", "    .filter(ee.Filter.bounds(geometry))\n", "\n", "gmw_image = gmw_filtered.first()\n", "\n", "# 添加参考数据到地图\n", "Map.addLayer(\n", "    gmw_image.clip(geometry),\n", "    mangrove_vis, \n", "    'Mangroves (Global Mangrove Watch)'\n", ")\n", "\n", "print(\"已添加Global Mangrove Watch参考数据\")\n", "print(\"- 数据来源：JAXA L波段SAR\")\n", "print(\"- 时间范围：1996-2020年\")\n", "print(\"- 用途：验证我们的分类结果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 结果分析\n", "\n", "比较我们的卫星嵌入分类结果与参考数据。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["分类统计信息：\n", "- 可以通过getInfo()查看详细的像素统计\n", "\n", "结果评估：\n", "- 全球红树林观测结果与卫星嵌入数据集的少样本分类输出结果非常接近\n", "- 卫星嵌入分类还捕获了全球红树林观测分类中缺失的更精细的景观细节\n", "- 使用少量训练样本就获得了高质量的分类结果\n"]}], "source": ["# 计算分类统计信息\n", "# 计算每个类别的像素数量\n", "class_areas = classified.reduceRegion(**{\n", "    'reducer': ee.Reducer.frequencyHistogram(),\n", "    'geometry': geometry,\n", "    'scale': 10,\n", "    'maxPixels': 1e10\n", "})\n", "\n", "print(\"分类统计信息：\")\n", "print(\"- 可以通过getInfo()查看详细的像素统计\")\n", "# print(class_areas.getInfo())  # 取消注释以查看详细统计\n", "\n", "print(\"\\n结果评估：\")\n", "print(\"- 全球红树林观测结果与卫星嵌入数据集的少样本分类输出结果非常接近\")\n", "print(\"- 卫星嵌入分类还捕获了全球红树林观测分类中缺失的更精细的景观细节\")\n", "print(\"- 使用少量训练样本就获得了高质量的分类结果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 显示交互式地图\n", "\n", "地图包含以下图层，可以切换查看不同结果："]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["地图图层说明：\n", "1. Study Area - 研究区域边界（红色）\n", "2. S2 Composite (False Color) - Sentinel-2假彩色合成影像\n", "3. Mangrove/Water/Other Samples - 训练样本点\n", "4. Classified Satellite Embeddings Image - 完整分类结果\n", "5. Mangroves Map (Satellite Embedding Classification) - 红树林分布图\n", "6. Mangroves (Global Mangrove Watch) - 参考数据\n", "\n", "图例：\n", "- 绿色：红树林\n", "- 蓝色：水体\n", "- 灰色：其他土地覆盖类型\n", "\n", "可以在地图界面中切换不同图层进行比较分析\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1fb8670cdeca466dabe5f2154ed80f0c", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(bottom=1074744.0, center=[-4.436135270804017, 39.520849999999996], controls=(WidgetControl(options=['posit…"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"地图图层说明：\")\n", "print(\"1. Study Area - 研究区域边界（红色）\")\n", "print(\"2. S2 Composite (False Color) - Sentinel-2假彩色合成影像\")\n", "print(\"3. Mangrove/Water/Other Samples - 训练样本点\")\n", "print(\"4. Classified Satellite Embeddings Image - 完整分类结果\")\n", "print(\"5. Mangroves Map (Satellite Embedding Classification) - 红树林分布图\")\n", "print(\"6. Mangroves (Global Mangrove Watch) - 参考数据\")\n", "print(\"\\n图例：\")\n", "print(\"- 绿色：红树林\")\n", "print(\"- 蓝色：水体\")\n", "print(\"- 灰色：其他土地覆盖类型\")\n", "print(\"\\n可以在地图界面中切换不同图层进行比较分析\")\n", "\n", "# 显示地图\n", "Map"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}