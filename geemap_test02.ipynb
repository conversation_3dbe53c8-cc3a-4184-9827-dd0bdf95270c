{"cells": [{"cell_type": "code", "execution_count": 4, "id": "53fad970-ae05-42b5-856d-82a814bb0a99", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import geemap\n", "import ee\n", "geemap.set_proxy(port=33210)\n", "geemap.ee_initialize()"]}, {"cell_type": "code", "execution_count": 2, "id": "f489fbda-234a-42fd-9371-23f52502063e", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/214a2e89d3bd316d07c5d51f71a2b88e-96e763b1b0bfa59884206dae8d76d9ab:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\landsat_crs2.tif\n"]}], "source": ["Map = geemap.Map()\n", "image = ee.Image('LANDSAT/LC08/C02/T1_TOA/LC08_044034_20140318').select(\n", "    ['B5', 'B4', 'B3']\n", ")\n", "region = ee.Geometry.BBox(-122.5955, 37.5339, -122.0982, 37.8252)\n", "fc = ee.FeatureCollection(region)\n", "projection = image.select(0).projection().getInfo()\n", "crs = projection['crs']\n", "crs_transform = projection['transform']\n", "geemap.ee_export_image(\n", "    image,\n", "    filename=\"landsat_crs2.tif\",\n", "    crs=crs,\n", "    crs_transform=crs_transform,\n", "    region=region,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "18577c69-be17-4a2f-a428-8ac776e421a5", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 1/16: Downloads/landsat_01.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "711a03f763e04c1d82b2072c14cf52c4", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_01.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 2/16: Downloads/landsat_02.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "69c26e89fb044d699b7e02a1b0112903", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_02.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 3/16: Downloads/landsat_03.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb0a0c08958346258e41fca0a0fe3e0e", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_03.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 4/16: Downloads/landsat_04.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2db993cfdbac407d906ea2f1a283f8f3", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_04.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 5/16: Downloads/landsat_05.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "105a3904d96b4fdd9bb29946fe5c3c56", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_05.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 6/16: Downloads/landsat_06.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ac4f0db88bb4fddae21b3cdd48e8f2d", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_06.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 7/16: Downloads/landsat_07.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fe58ed2d53434a15bd456b01fd32bd69", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_07.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 8/16: Downloads/landsat_08.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d82bf0bca7784b11a8575228c0bfbe53", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_08.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 9/16: Downloads/landsat_09.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb167743d8b746f0afc178c0cba58749", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_09.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 10/16: Downloads/landsat_10.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bfc8a493b78e41cba156e0c38fc330cb", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_10.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 11/16: Downloads/landsat_11.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be49e0dfdd7b46ea9b47e0b4bbe4718a", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_11.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 12/16: Downloads/landsat_12.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e11fa20630e342bf910bce758a18952c", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_12.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 13/16: Downloads/landsat_13.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "de6a848f3fe54b17981efabd2ea5f4f0", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_13.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 14/16: Downloads/landsat_14.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9e28069ee9b34c9488919a3d735a7ed9", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_14.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 15/16: Downloads/landsat_15.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "95bb027667fe435dac3c570f8a99ef11", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_15.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading 16/16: Downloads/landsat_16.tif\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c63d80e69914268ad1f369f32a1c18a", "version_major": 2, "version_minor": 0}, "text/plain": ["landsat_16.tif: |                                                     | 0.00/57.1M (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded 16 tiles in 112.80615186691284 seconds.\n"]}], "source": ["fishnet = geemap.fishnet(image.geometry(), rows=4, cols=4, delta=0.5)\n", "style = {'color': 'ffff00ff', 'fillColor': '00000000'}\n", "Map.addLayer(fishnet.style(**style), {}, 'Fishnet')\n", "out_dir = os.path.expanduser('Downloads/')\n", "geemap.download_ee_image_tiles(\n", "    image, fishnet, out_dir, prefix=\"landsat_\", crs=\"EPSG:4326\", scale=30\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "74ffd612-62f5-4400-84a6-d088f6db3137", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["path = r'E:\\Heritage\\FloodArea\\lyy\\1\\area1.shp'\n", "fc = geemap.shp_to_ee(path)\n", "Map.centerObject(fc)\n", "fishnet = geemap.fishnet(fc, rows=4, cols=4, delta=0.5)\n", "style = {'color': 'ffff00ff', 'fillColor': '00000000'}\n", "Map.addLayer(fishnet.style(**style), {}, 'Fishnet')"]}, {"cell_type": "code", "execution_count": 11, "id": "86593efa-4ec1-4646-a636-7c8ad3b5de26", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total number of images: 7\n", "\n", "Exporting 1/7: Downloads/m_4609915_sw_14_1_20090818.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/312202f5a0ba8ed149d1fbfa3f8104e7-4220ea9f0fa5304ab08ccf48df907ae0:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_1_20090818.tif\n", "\n", "\n", "Exporting 2/7: Downloads/m_4609915_sw_14_1_20100629.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/c283b6bbbcd742e97344cca15eee6566-b0f87de90b73bc7d53b649e24a73446c:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_1_20100629.tif\n", "\n", "\n", "Exporting 3/7: Downloads/m_4609915_sw_14_1_20120714.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/a26f4884098c3adb79654748ef59afd4-3763477adbc020db1c404fc647850244:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_1_20120714.tif\n", "\n", "\n", "Exporting 4/7: Downloads/m_4609915_sw_14_1_20140901.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/ccc8b276f47a978c2e30c891bb4f0a6d-cd085730e18b1c6f6559c0e9e7188ac6:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_1_20140901.tif\n", "\n", "\n", "Exporting 5/7: Downloads/m_4609915_sw_14_1_20150926.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/a3e385a83d7850b8445f4f5728378e58-b16c7a555b46eece8d620f56d1bed0df:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_1_20150926.tif\n", "\n", "\n", "Exporting 6/7: Downloads/m_4609915_sw_14_h_20160704.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/8f0056abb9c25e4317f6ea3791167970-bce82a31872c75963c33e508dcd98636:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_h_20160704.tif\n", "\n", "\n", "Exporting 7/7: Downloads/m_4609915_sw_14_h_20170703.tif\n", "Generating URL ...\n", "Downloading data from https://earthengine.googleapis.com/v1/projects/825775132399/thumbnails/5084a23b14e0093c7d842e43882f6d4c-c741e97f6fd8ba00bebd6f870576cd6c:getPixels\n", "Please wait ...\n", "Data downloaded to G:\\paper3\\geemap\\Downloads\\m_4609915_sw_14_h_20170703.tif\n", "\n", "\n"]}], "source": ["point = ee.Geometry.Point(-99.2222, 46.7816)\n", "collection = (\n", "    ee.ImageCollection('USDA/NAIP/DOQQ')\n", "    .filterBounds(point)\n", "    .filterDate('2008-01-01', '2018-01-01')\n", "    .filter(ee.Filter.listContains(\"system:band_names\", \"N\"))\n", ")\n", "out_dir = os.path.expanduser('Downloads/')\n", "geemap.ee_export_image_collection(collection, out_dir=out_dir, scale=10)"]}, {"cell_type": "code", "execution_count": 12, "id": "020aad3d-b9d4-471e-9942-abeeb3aef875", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total number of images: 7\n", "\n"]}], "source": ["geemap.ee_export_image_collection_to_drive(collection, folder='export', scale=10)"]}, {"cell_type": "code", "execution_count": 7, "id": "2e1a14f0-bb57-4e53-82bd-dce34ce4c495", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "212498c2105443d9baad326fde9ca92e", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[0, 0], controls=(WidgetControl(options=['position', 'transparent_bg'], widget=SearchDataGUI(childr…"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Map"]}, {"cell_type": "code", "execution_count": null, "id": "624f82ee-ad81-4ec1-adfe-5ed28381f778", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}