{"cells": [{"cell_type": "code", "execution_count": 1, "id": "46369455-01f2-4707-9bf6-00c341c1f5c3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import os\n", "import geemap\n", "import ee\n", "geemap.set_proxy(port=33210)\n", "geemap.ee_initialize()"]}, {"cell_type": "markdown", "id": "fa0038a2-3bd4-46a7-a050-b4460c125c20", "metadata": {}, "source": ["2.2 加载数据\n", "这里我们仍然使用上节 GEE 教程（五）使用的淮南市为目标区域，使用 Sentinel-2 Level-2A  级数据产品，并裁剪出目标区域的影像。"]}, {"cell_type": "code", "execution_count": 2, "id": "f22cb6d5-ec2e-4742-9347-2226eba62715", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["s2 = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')\n", "path = r'E:\\Heritage\\FloodArea\\lyy\\1\\area1.shp'\n", "huainan = geemap.shp_to_ee(path)"]}, {"cell_type": "markdown", "id": "69d8d906-7731-4022-b025-008660e61529", "metadata": {}, "source": ["2.3 数据预处理\n", "1、将 ee.FeatureCollection 转换为 ee.geometry 格式的最小外接矩形"]}, {"cell_type": "code", "execution_count": 3, "id": "08b14ebc-aeb5-4187-9d1c-bb342044866c", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["roi = huainan.geometry().bounds()"]}, {"cell_type": "markdown", "id": "3ecc68d6-3fd4-4361-bf1c-071e9d180c55", "metadata": {}, "source": ["2、借助 Sentinel-2 数据中的 QA60 字段去云，这是 GEE 官网提示的去云函数"]}, {"cell_type": "code", "execution_count": 4, "id": "d81ea558-6854-4abc-b533-cb9d641b2d81", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def maskS2clouds(image):\n", "   qa = image.select('QA60')\n", "   # Bits 10 and 11是云，我们要把它mask掉\n", "   cloudBitMask = 1 << 10\n", "   cirrusBitMask = 1 << 1\n", "   # 这两个标志都应该设置为0，表示条件明确。\n", "   mask = qa.bitwiseAnd(cloudBitMask).eq(0) \\\n", "      .And(qa.bitwiseAnd(cirrusBitMask).eq(0))\n", "   # 哨兵的像元值是反射率的10000倍，要除以10000\n", "   return image.updateMask(mask).divide(10000)"]}, {"cell_type": "markdown", "id": "555fd2b4-b6ed-4f84-8477-5ff617ecc6ca", "metadata": {}, "source": ["3、筛选出待下载的哨兵影像数据集"]}, {"cell_type": "code", "execution_count": 5, "id": "f1af39ca-b527-4831-a369-3a004b1876a0", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["collection = s2.filterDate('2022-01-01', '2022-12-31') \\\n", "            .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10)) \\\n", "            .filterBounds(huainan) \\\n", "            .map(maskS2clouds) \\\n", "            .select('B[1-8]')"]}, {"cell_type": "markdown", "id": "82b7ad1f-5817-4870-9370-69dbdd2535eb", "metadata": {}, "source": ["时间段 2022 年全年、云层覆盖率小于 10%、以目标区域为边界、遍历去云函数、选取 1~8 波段"]}, {"cell_type": "markdown", "id": "0ed71a88-5e8e-4552-84dc-3e247f4db390", "metadata": {}, "source": ["4、将影像数据集计算中值后得到的单幅影像针对目标区域进行裁剪，得到最终待下载数据"]}, {"cell_type": "code", "execution_count": 6, "id": "9caa2236-f054-4fc4-9498-ced9c90fa7f0", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["composite = collection.median().clip(roi)"]}, {"cell_type": "markdown", "id": "c6da94ca-4ed3-4a59-bf2f-f78409a7cad1", "metadata": {}, "source": ["2.4 数据显示\n", "\n", "1、在下载数据之前，我们可以先查看待下载的影像的显示效果。首先我们设置栅格和矢量的可视化参数，查看待下载影像的 RGB 真彩色图像。"]}, {"cell_type": "code", "execution_count": 7, "id": "9e3f4db4-d47f-470a-b0f8-1913e855a0e1", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rgbVis = {\n", " 'min': 0.0,\n", " 'max': 0.3,\n", " 'bands': ['B4', 'B3', 'B2'],\n", "}\n", "# 设置显示样式：color代表边界颜色；fillcolor代表填充颜色\n", "styling = {\n", "   'color': 'red',\n", "   'fillColor': '00000000'\n", "}"]}, {"cell_type": "markdown", "id": "64aee577-63da-4a1c-a48f-84b5da67fa5c", "metadata": {}, "source": ["2、随后创建一个 Map 实例，将栅格和矢量添加到图层中。"]}, {"cell_type": "code", "execution_count": 8, "id": "2a100161-b558-408b-83ce-ec7c40911f6d", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ecff64a7ef094bec8c23050345059496", "version_major": 2, "version_minor": 0}, "text/plain": ["Map(center=[31.988839110162058, 96.3253932879723], controls=(WidgetControl(options=['position', 'transparent_b…"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["Map = geemap.Map()\n", "Map.addLayer(composite, rgbVis, '淮南市S2影像')\n", "Map.addLayer(huainan.style(**styling), {}, '淮南市边界')\n", "Map.centerObject(huainan, 9)\n", "Map"]}, {"cell_type": "markdown", "id": "206c65e1-c95b-4b6c-9a50-d45c74065296", "metadata": {}, "source": ["3 下载数据\n", "3.1 配置输出目录\n", "在确认待下载影像无误后，我们只需要设置好输出目录，就可以开始下载数据了。"]}, {"cell_type": "code", "execution_count": 10, "id": "817bf661-2bb3-4eef-8727-476f18c76550", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["work_dir = os.path.join(os.path.expanduser(\"Downloads/geeDownloads\"), 'tif')\n", "if not os.path.exists(work_dir):\n", "   os.makedirs(work_dir)\n", "out_tif = os.path.join(work_dir, \"S2_SR_2022_huainan.tif\")"]}, {"cell_type": "markdown", "id": "19af2141-94f5-4b2e-813a-b16f39052f58", "metadata": {}, "source": ["3.2 下载数据\n", "直接使用 download_ee_image() 函数，并设置好关键参数，运行后就可以开始无限制下载 GEE 平台数据了"]}, {"cell_type": "code", "execution_count": 12, "id": "46182e72-5845-4c65-abf9-ab0301c0229f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "            <style>\n", "                .geemap-dark {\n", "                    --jp-widgets-color: white;\n", "                    --jp-widgets-label-color: white;\n", "                    --jp-ui-font-color1: white;\n", "                    --jp-layout-color2: #454545;\n", "                    background-color: #383838;\n", "                }\n", "\n", "                .geemap-dark .jupyter-button {\n", "                    --jp-layout-color3: #383838;\n", "                }\n", "\n", "                .geemap-colab {\n", "                    background-color: var(--colab-primary-surface-color, white);\n", "                }\n", "\n", "                .geemap-colab .jupyter-button {\n", "                    --jp-layout-color3: var(--colab-primary-surface-color, white);\n", "                }\n", "            </style>\n", "            "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Consider adjusting `region`, `scale` and/or `dtype` to reduce the S2_SR_2022_huainan.tif download size (raw: 4.80 GB).\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bd4d07d9f64442d1b79e22413e27d99a", "version_major": 2, "version_minor": 0}, "text/plain": ["S2_SR_2022_huainan.tif: |                                             | 0.00/4.80G (raw) [  0.0%] in 00:00 (et…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["There is no STAC entry for: None\n"]}], "source": ["geemap.download_ee_image(\n", "   image=composite,\n", "   filename=out_tif,\n", "   region=roi,\n", "   crs=\"EPSG:4326\",\n", "   scale=100,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4c2fa7bd-6dc8-4871-a4bc-97edbf2a2cde", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}