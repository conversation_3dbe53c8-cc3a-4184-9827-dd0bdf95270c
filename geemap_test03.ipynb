import os
import geemap
import ee
geemap.set_proxy(port=33210)
geemap.ee_initialize()

s2 = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
path = r'E:\Heritage\FloodArea\lyy\1\area1.shp'
huainan = geemap.shp_to_ee(path)

roi = huainan.geometry().bounds()

def maskS2clouds(image):
   qa = image.select('QA60')
   # Bits 10 and 11是云，我们要把它mask掉
   cloudBitMask = 1 << 10
   cirrusBitMask = 1 << 1
   # 这两个标志都应该设置为0，表示条件明确。
   mask = qa.bitwiseAnd(cloudBitMask).eq(0) \
      .And(qa.bitwiseAnd(cirrusBitMask).eq(0))
   # 哨兵的像元值是反射率的10000倍，要除以10000
   return image.updateMask(mask).divide(10000)

collection = s2.filterDate('2022-01-01', '2022-12-31') \
            .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10)) \
            .filterBounds(huainan) \
            .map(maskS2clouds) \
            .select('B[1-8]')

composite = collection.median().clip(roi)

rgbVis = {
 'min': 0.0,
 'max': 0.3,
 'bands': ['B4', 'B3', 'B2'],
}
# 设置显示样式：color代表边界颜色；fillcolor代表填充颜色
styling = {
   'color': 'red',
   'fillColor': '00000000'
}

Map = geemap.Map()
Map.addLayer(composite, rgbVis, '淮南市S2影像')
Map.addLayer(huainan.style(**styling), {}, '淮南市边界')
Map.centerObject(huainan, 9)
Map

work_dir = os.path.join(os.path.expanduser("Downloads/geeDownloads"), 'tif')
if not os.path.exists(work_dir):
   os.makedirs(work_dir)
out_tif = os.path.join(work_dir, "S2_SR_2022_huainan.tif")

geemap.download_ee_image(
   image=composite,
   filename=out_tif,
   region=roi,
   crs="EPSG:4326",
   scale=100,
)

