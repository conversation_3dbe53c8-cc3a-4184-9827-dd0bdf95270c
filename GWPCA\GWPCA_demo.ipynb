{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 地理加权主成分分析(GWPCA)演示\n", "\n", "## 算法概述\n", "本演示展示了如何使用地理加权主成分分析将高维卫星嵌入数据降维到三维。\n", "\n", "## 核心特点\n", "1. 考虑空间邻近性的局部主成分分析\n", "2. 使用高斯核函数计算空间权重\n", "3. 保留空间变异的主要模式"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义GWPCA核心函数\n", "def gaussian_kernel(distances, bandwidth):\n", "    \"\"\"高斯核函数计算空间权重\"\"\"\n", "    return np.exp(-(distances**2) / (2 * bandwidth**2))\n", "\n", "def gwpca_at_location(target_coords, all_coords, all_data, bandwidth, n_components=3):\n", "    \"\"\"在指定位置执行地理加权主成分分析\"\"\"\n", "    # 计算距离\n", "    distances = cdist([target_coords], all_coords, metric='euclidean')[0]\n", "    \n", "    # 计算权重\n", "    weights = gaussian_kernel(distances, bandwidth)\n", "    weights = weights / np.sum(weights)\n", "    \n", "    # 加权数据标准化\n", "    weighted_mean = np.average(all_data, weights=weights, axis=0)\n", "    centered_data = all_data - weighted_mean\n", "    \n", "    # 计算加权协方差矩阵\n", "    weighted_cov = np.cov(centered_data.T, aweights=weights)\n", "    \n", "    # 特征值分解\n", "    eigenvalues, eigenvectors = np.linalg.eigh(weighted_cov)\n", "    \n", "    # 按特征值降序排列\n", "    idx = np.argsort(eigenvalues)[::-1]\n", "    eigenvalues = eigenvalues[idx]\n", "    eigenvectors = eigenvectors[:, idx]\n", "    \n", "    # 选择前n_components个主成分\n", "    components = eigenvectors[:, :n_components]\n", "    explained_variance = eigenvalues[:n_components]\n", "    explained_variance_ratio = explained_variance / np.sum(eigenvalues)\n", "    \n", "    # 变换目标位置的数据\n", "    target_idx = np.argmin(distances)\n", "    target_data = all_data[target_idx] - weighted_mean\n", "    transformed_data = np.dot(target_data, components)\n", "    \n", "    return components, explained_variance_ratio, transformed_data\n", "\n", "print(\"GWPCA函数定义完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成模拟的丝绸之路流域数据\n", "print(\"=== 生成模拟数据 ===\")\n", "\n", "# 设置随机种子以确保结果可重现\n", "np.random.seed(42)\n", "\n", "# 模拟丝绸之路流域的地理范围\n", "lon_range = [60, 120]  # 经度范围\n", "lat_range = [25, 50]   # 纬度范围\n", "\n", "# 生成0.25度分辨率的网格点\n", "lons = np.arange(lon_range[0], lon_range[1], 0.25)\n", "lats = np.arange(lat_range[0], lat_range[1], 0.25)\n", "\n", "# 创建网格\n", "lon_grid, lat_grid = np.meshgrid(lons, lats)\n", "coordinates = np.column_stack([lon_grid.ravel(), lat_grid.ravel()])\n", "\n", "# 模拟64维卫星嵌入数据\n", "n_samples = len(coordinates)\n", "n_features = 64\n", "\n", "# 生成具有空间相关性的数据\n", "embeddings_data = np.random.randn(n_samples, n_features)\n", "\n", "# 添加空间趋势\n", "for i in range(n_features):\n", "    # 添加经度趋势\n", "    embeddings_data[:, i] += 0.1 * coordinates[:, 0] / 100\n", "    # 添加纬度趋势\n", "    embeddings_data[:, i] += 0.1 * coordinates[:, 1] / 50\n", "\n", "print(f\"模拟数据生成完成:\")\n", "print(f\"- 样本数量: {n_samples}\")\n", "print(f\"- 特征维度: {n_features}\")\n", "print(f\"- 经度范围: {coordinates[:, 0].min():.1f} - {coordinates[:, 0].max():.1f}\")\n", "print(f\"- 纬度范围: {coordinates[:, 1].min():.1f} - {coordinates[:, 1].max():.1f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行GWPCA分析\n", "print(\"\\n=== 执行GWPCA分析 ===\")\n", "\n", "# 设置参数\n", "bandwidth = 2.0  # 带宽参数（度）\n", "n_components = 3  # 主成分数量\n", "\n", "print(f\"GWPCA参数:\")\n", "print(f\"- 带宽: {bandwidth}度\")\n", "print(f\"- 主成分数量: {n_components}\")\n", "\n", "# 选择一些代表性位置进行分析\n", "test_locations = [\n", "    [70, 30],   # 西南\n", "    [90, 40],   # 中心\n", "    [110, 45],  # 东北\n", "    [80, 35],   # 中西\n", "    [100, 30]   # 中东\n", "]\n", "\n", "results = []\n", "\n", "print(f\"\\n开始分析 {len(test_locations)} 个测试位置...\")\n", "\n", "for i, location in enumerate(test_locations):\n", "    print(f\"分析位置 {i+1}: [{location[0]}, {location[1]}]\")\n", "    \n", "    try:\n", "        components, var_ratios, transformed = gwpca_at_location(\n", "            location, coordinates, embeddings_data, bandwidth, n_components\n", "        )\n", "        \n", "        results.append({\n", "            'location': location,\n", "            'components': components,\n", "            'var_ratios': var_ratios,\n", "            'transformed': transformed\n", "        })\n", "        \n", "        print(f\"  - 解释方差比: {var_ratios}\")\n", "        print(f\"  - 累计解释方差: {var_ratios.sum():.4f}\")\n", "        print(f\"  - 降维后数据: {transformed}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  - 分析失败: {e}\")\n", "\n", "print(f\"\\nGWPCA分析完成，成功分析 {len(results)} 个位置\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化结果\n", "print(\"\\n=== 结果可视化 ===\")\n", "\n", "# 创建图形\n", "fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n", "fig.suptitle('地理加权主成分分析(GWPCA)结果', fontsize=16)\n", "\n", "# 提取结果数据\n", "locations = [r['location'] for r in results]\n", "pc1_values = [r['transformed'][0] for r in results]\n", "pc2_values = [r['transformed'][1] for r in results]\n", "pc3_values = [r['transformed'][2] for r in results]\n", "var_ratios_1 = [r['var_ratios'][0] for r in results]\n", "var_ratios_2 = [r['var_ratios'][1] for r in results]\n", "var_ratios_3 = [r['var_ratios'][2] for r in results]\n", "\n", "# 转换为数组\n", "locations = np.array(locations)\n", "lons = locations[:, 0]\n", "lats = locations[:, 1]\n", "\n", "# 绘制第一主成分\n", "scatter1 = axes[0, 0].scatter(lons, lats, c=pc1_values, cmap='viridis', s=100)\n", "axes[0, 0].set_title('第一主成分 (PC1)')\n", "axes[0, 0].set_xlabel('经度')\n", "axes[0, 0].set_ylabel('纬度')\n", "plt.colorbar(scatter1, ax=axes[0, 0])\n", "\n", "# 绘制第二主成分\n", "scatter2 = axes[0, 1].scatter(lons, lats, c=pc2_values, cmap='plasma', s=100)\n", "axes[0, 1].set_title('第二主成分 (PC2)')\n", "axes[0, 1].set_xlabel('经度')\n", "axes[0, 1].set_ylabel('纬度')\n", "plt.colorbar(scatter2, ax=axes[0, 1])\n", "\n", "# 绘制第三主成分\n", "scatter3 = axes[0, 2].scatter(lons, lats, c=pc3_values, cmap='coolwarm', s=100)\n", "axes[0, 2].set_title('第三主成分 (PC3)')\n", "axes[0, 2].set_xlabel('经度')\n", "axes[0, 2].set_ylabel('纬度')\n", "plt.colorbar(scatter3, ax=axes[0, 2])\n", "\n", "# 绘制解释方差比\n", "x_pos = np.arange(len(results))\n", "width = 0.25\n", "\n", "axes[1, 0].bar(x_pos - width, var_ratios_1, width, label='PC1', alpha=0.8)\n", "axes[1, 0].bar(x_pos, var_ratios_2, width, label='PC2', alpha=0.8)\n", "axes[1, 0].bar(x_pos + width, var_ratios_3, width, label='PC3', alpha=0.8)\n", "axes[1, 0].set_title('各位置的解释方差比')\n", "axes[1, 0].set_xlabel('位置编号')\n", "axes[1, 0].set_ylabel('解释方差比')\n", "axes[1, 0].legend()\n", "axes[1, 0].set_xticks(x_pos)\n", "axes[1, 0].set_xticklabels([f'位置{i+1}' for i in range(len(results))])\n", "\n", "# 绘制累计解释方差\n", "cumulative_var = [sum(r['var_ratios']) for r in results]\n", "axes[1, 1].bar(x_pos, cumulative_var, alpha=0.8, color='orange')\n", "axes[1, 1].set_title('累计解释方差比')\n", "axes[1, 1].set_xlabel('位置编号')\n", "axes[1, 1].set_ylabel('累计解释方差比')\n", "axes[1, 1].set_xticks(x_pos)\n", "axes[1, 1].set_xticklabels([f'位置{i+1}' for i in range(len(results))])\n", "\n", "# 绘制3D散点图\n", "ax_3d = fig.add_subplot(2, 3, 6, projection='3d')\n", "ax_3d.scatter(pc1_values, pc2_values, pc3_values, c=cumulative_var, cmap='viridis', s=100)\n", "ax_3d.set_title('三维主成分空间')\n", "ax_3d.set_xlabel('PC1')\n", "ax_3d.set_ylabel('PC2')\n", "ax_3d.set_zlabel('PC3')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"可视化完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成分析报告\n", "print(\"\\n=== GWPCA分析报告 ===\")\n", "print(f\"\\n1. 数据概况:\")\n", "print(f\"   - 研究区域：丝绸之路流域 (60°-120°E, 25°-50°N)\")\n", "print(f\"   - 空间分辨率：0.25°\")\n", "print(f\"   - 总样本数：{n_samples}\")\n", "print(f\"   - 原始维度：{n_features}维\")\n", "print(f\"   - 降维后维度：{n_components}维\")\n", "\n", "print(f\"\\n2. GWPCA参数:\")\n", "print(f\"   - 空间权重函数：高斯核\")\n", "print(f\"   - 带宽参数：{bandwidth}度\")\n", "print(f\"   - 分析位置数：{len(results)}\")\n", "\n", "print(f\"\\n3. 主成分统计:\")\n", "for i in range(n_components):\n", "    pc_values = [r['transformed'][i] for r in results]\n", "    var_ratios = [r['var_ratios'][i] for r in results]\n", "    print(f\"   第{i+1}主成分 (PC{i+1}):\")\n", "    print(f\"     - 数值范围：{min(pc_values):.4f} - {max(pc_values):.4f}\")\n", "    print(f\"     - 平均解释方差比：{np.mean(var_ratios):.4f}\")\n", "    print(f\"     - 标准差：{np.std(pc_values):.4f}\")\n", "\n", "print(f\"\\n4. 总体性能:\")\n", "avg_cumulative = np.mean([sum(r['var_ratios']) for r in results])\n", "print(f\"   - 平均累计解释方差比：{avg_cumulative:.4f}\")\n", "print(f\"   - 信息保留率：{avg_cumulative*100:.2f}%\")\n", "\n", "print(f\"\\n5. 空间变异性:\")\n", "pc1_spatial_var = np.var([r['transformed'][0] for r in results])\n", "pc2_spatial_var = np.var([r['transformed'][1] for r in results])\n", "pc3_spatial_var = np.var([r['transformed'][2] for r in results])\n", "print(f\"   - PC1空间方差：{pc1_spatial_var:.4f}\")\n", "print(f\"   - PC2空间方差：{pc2_spatial_var:.4f}\")\n", "print(f\"   - PC3空间方差：{pc3_spatial_var:.4f}\")\n", "\n", "print(f\"\\n=== 分析完成 ===\")\n", "print(f\"\\n算法特点:\")\n", "print(f\"- ✓ 考虑空间邻近性的局部主成分分析\")\n", "print(f\"- ✓ 保留了主要的空间变异模式\")\n", "print(f\"- ✓ 有效降维：从{n_features}维降至{n_components}维\")\n", "print(f\"- ✓ 空间自适应：不同位置有不同的主成分\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}