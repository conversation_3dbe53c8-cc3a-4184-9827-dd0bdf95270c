{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 修复版：下载全球逐年逐波段Satellite Embedding 0.25°数据\n", "\n", "## 修复内容\n", "- 修复全球数据只有1列720行的问题\n", "- 正确设置全球投影和分辨率\n", "- 优化下载参数\n", "\n", "## 预期结果\n", "- 全球0.25°数据应该是 1440列 × 720行\n", "- 经度范围：-180° 到 180° (1440个像素)\n", "- 纬度范围：-90° 到 90° (720个像素)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import ee\n", "import geemap\n", "import os\n", "from pathlib import Path\n", "import time\n", "\n", "# 初始化Earth Engine\n", "geemap.set_proxy(port=33210)\n", "ee.Initialize()\n", "\n", "print(\"Earth Engine已初始化\")\n", "print(\"开始准备下载修复版全球Satellite Embedding数据\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置修复版参数\n", "print(\"=== 设置修复版全球下载参数 ===\")\n", "\n", "# 目标路径\n", "base_path = r\"H:\\satellite_embedding\\global_025_fixed\"\n", "\n", "# 测试参数\n", "start_year = 2022\n", "end_year = 2022\n", "years = list(range(start_year, end_year + 1))\n", "bands = ['A00']  # 先测试单个波段\n", "\n", "# 修复：使用更精确的全球边界定义\n", "# 避免极地区域可能的投影问题\n", "roi = ee.Geometry.Rectangle([-180, -85, 180, 85])  # 使用Rectangle而非Polygon\n", "\n", "# 修复：使用正确的投影和分辨率参数\n", "crs = \"EPSG:4326\"  # WGS84地理坐标系\n", "# 0.25度 = 0.25 * 111319.9 米 ≈ 27830米（在赤道）\n", "# 但对于全球数据，直接使用度数更准确\n", "scale = None  # 让Earth Engine自动计算\n", "crs_transform = [0.25, 0, -180, 0, -0.25, 85]  # 直接指定变换矩阵\n", "\n", "print(f\"目标路径: {base_path}\")\n", "print(f\"年份: {years}\")\n", "print(f\"波段: {bands}\")\n", "print(f\"研究区域: 全球 (-180° to 180°E, -85° to 85°N)\")\n", "print(f\"分辨率: 0.25° (1440×680像素)\")\n", "print(f\"投影: {crs}\")\n", "print(f\"变换矩阵: {crs_transform}\")\n", "\n", "# 创建基础目录\n", "Path(base_path).mkdir(parents=True, exist_ok=True)\n", "print(f\"已创建基础目录: {base_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建年份目录\n", "year_paths = {}\n", "for year in years:\n", "    year_path = os.path.join(base_path, str(year))\n", "    Path(year_path).mkdir(parents=True, exist_ok=True)\n", "    year_paths[year] = year_path\n", "    print(f\"已创建目录: {year_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 修复版下载函数\n", "def download_global_band_fixed(year, band, roi, output_path, crs, crs_transform):\n", "    \"\"\"\n", "    修复版：下载全球satellite embedding数据\n", "    \"\"\"\n", "    try:\n", "        print(f\"    正在准备 {year}年 {band}波段的全球数据...\")\n", "        \n", "        # 加载satellite embedding数据集\n", "        embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "        \n", "        # 设置时间范围\n", "        start_date = ee.Date.fromYMD(year, 1, 1)\n", "        end_date = start_date.advance(1, 'year')\n", "        \n", "        # 过滤数据\n", "        filtered = embeddings.filter(ee.Filter.date(start_date, end_date))\n", "        \n", "        # 创建镶嵌图并选择指定波段\n", "        image = filtered.mosaic().select([band])\n", "        \n", "        # 修复：设置正确的投影\n", "        image = image.reproject(crs=crs, crsTransform=crs_transform)\n", "        \n", "        print(f\"    数据准备完成，开始下载全球数据...\")\n", "        print(f\"    预期尺寸: 1440×680像素\")\n", "        \n", "        # 使用修复的下载参数\n", "        geemap.download_ee_image(\n", "            image=image,\n", "            filename=output_path,\n", "            region=roi,\n", "            crs=crs,\n", "            crs_transform=crs_transform,\n", "            file_per_band=False\n", "        )\n", "        \n", "        # 验证下载结果\n", "        if os.path.exists(output_path):\n", "            file_size = os.path.getsize(output_path)\n", "            print(f\"    ✓ 下载成功: {output_path}\")\n", "            print(f\"    文件大小: {file_size/1024/1024:.1f} MB\")\n", "            \n", "            # 验证数据维度\n", "            try:\n", "                import rasterio\n", "                with rasterio.open(output_path) as src:\n", "                    print(f\"    数据维度: {src.width}×{src.height}\")\n", "                    print(f\"    投影: {src.crs}\")\n", "                    print(f\"    边界: {src.bounds}\")\n", "                    \n", "                    if src.width == 1440 and src.height == 680:\n", "                        print(f\"    ✓ 数据维度正确！\")\n", "                    else:\n", "                        print(f\"    ⚠️ 数据维度异常，预期1440×680\")\n", "            except ImportError:\n", "                print(f\"    (无法验证维度，需要安装rasterio)\")\n", "            \n", "            return True, None\n", "        else:\n", "            return False, \"文件未创建\"\n", "        \n", "    except Exception as e:\n", "        return False, str(e)\n", "\n", "print(\"修复版下载函数已定义\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行修复版下载测试\n", "print(\"\\n=== 执行修复版下载测试 ===\")\n", "\n", "for year in years:\n", "    print(f\"\\n--- 测试 {year} 年 ---\")\n", "    year_path = year_paths[year]\n", "    \n", "    for band in bands:\n", "        filename = f\"{band}_{year}_global_fixed.tif\"\n", "        output_path = os.path.join(year_path, filename)\n", "        \n", "        print(f\"  测试下载 {band} -> {filename}\")\n", "        \n", "        # 执行下载\n", "        success, error = download_global_band_fixed(\n", "            year, band, roi, output_path, crs, crs_transform\n", "        )\n", "        \n", "        if success:\n", "            print(f\"    ✓ 测试成功\")\n", "        else:\n", "            print(f\"    ✗ 测试失败: {error}\")\n", "            \n", "            # 如果失败，尝试备用方法\n", "            print(f\"    尝试备用下载方法...\")\n", "            try:\n", "                # 备用方法：使用scale参数而非crs_transform\n", "                embeddings = ee.ImageCollection('GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL')\n", "                start_date = ee.Date.fromYMD(year, 1, 1)\n", "                end_date = start_date.advance(1, 'year')\n", "                filtered = embeddings.filter(ee.Filter.date(start_date, end_date))\n", "                image = filtered.mosaic().select([band])\n", "                \n", "                # 使用scale参数\n", "                geemap.download_ee_image(\n", "                    image=image,\n", "                    filename=output_path,\n", "                    region=roi,\n", "                    crs=crs,\n", "                    scale=27830,  # 约0.25度\n", "                    file_per_band=False\n", "                )\n", "                \n", "                if os.path.exists(output_path):\n", "                    print(f\"    ✓ 备用方法成功\")\n", "                else:\n", "                    print(f\"    ✗ 备用方法也失败\")\n", "                    \n", "            except Exception as e2:\n", "                print(f\"    ✗ 备用方法异常: {e2}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 详细验证下载结果\n", "print(\"\\n=== 详细验证下载结果 ===\")\n", "\n", "for year in years:\n", "    year_path = year_paths[year]\n", "    \n", "    for band in bands:\n", "        filename = f\"{band}_{year}_global_fixed.tif\"\n", "        file_path = os.path.join(year_path, filename)\n", "        \n", "        if os.path.exists(file_path):\n", "            file_size = os.path.getsize(file_path)\n", "            print(f\"\\n文件: {filename}\")\n", "            print(f\"大小: {file_size/1024/1024:.2f} MB\")\n", "            \n", "            # 使用rasterio详细检查\n", "            try:\n", "                import rasterio\n", "                with rasterio.open(file_path) as src:\n", "                    print(f\"维度: {src.width} × {src.height}\")\n", "                    print(f\"波段数: {src.count}\")\n", "                    print(f\"数据类型: {src.dtypes[0]}\")\n", "                    print(f\"投影: {src.crs}\")\n", "                    print(f\"边界: {src.bounds}\")\n", "                    print(f\"变换: {src.transform}\")\n", "                    \n", "                    # 检查数据是否正确\n", "                    if src.width == 1440 and src.height == 720:\n", "                        print(f\"✓ 维度完全正确 (1440×720)\")\n", "                    elif src.width == 1440 and src.height == 680:\n", "                        print(f\"✓ 维度基本正确 (1440×680, 排除极地)\")\n", "                    elif src.width == 1 and src.height == 720:\n", "                        print(f\"✗ 维度错误：只有1列720行 - 这是原问题\")\n", "                    else:\n", "                        print(f\"? 维度异常: {src.width}×{src.height}\")\n", "                    \n", "                    # 读取一些数据检查\n", "                    data = src.read(1, window=((0, min(10, src.height)), (0, min(10, src.width))))\n", "                    print(f\"数据样本形状: {data.shape}\")\n", "                    print(f\"数据范围: {data.min():.4f} - {data.max():.4f}\")\n", "                    \n", "            except ImportError:\n", "                print(f\"需要安装rasterio来详细检查数据\")\n", "            except Exception as e:\n", "                print(f\"检查数据时出错: {e}\")\n", "        else:\n", "            print(f\"\\n文件不存在: {filename}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 问题诊断和解决方案\n", "print(\"\\n=== 问题诊断和解决方案 ===\")\n", "\n", "print(\"如果下载的数据仍然只有1列720行，可能的原因和解决方案:\")\n", "print(\"\")\n", "print(\"1. 投影问题:\")\n", "print(\"   - 原因: Earth Engine在处理全球数据时的投影转换问题\")\n", "print(\"   - 解决: 使用不同的投影参数或分块下载\")\n", "print(\"\")\n", "print(\"2. 区域定义问题:\")\n", "print(\"   - 原因: 全球边界定义可能导致投影异常\")\n", "print(\"   - 解决: 使用更小的区域块或不同的边界定义\")\n", "print(\"\")\n", "print(\"3. Earth Engine限制:\")\n", "print(\"   - 原因: EE对大型全球数据的处理限制\")\n", "print(\"   - 解决: 分块下载或使用EE的导出功能\")\n", "print(\"\")\n", "print(\"建议的替代方案:\")\n", "print(\"1. 分块下载: 将全球分成多个区域块\")\n", "print(\"2. 使用ee.batch.Export: 通过GEE的导出功能\")\n", "print(\"3. 调整分辨率: 先用较低分辨率测试\")\n", "print(\"4. 检查数据源: 确认Satellite Embedding数据的原始分辨率\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}